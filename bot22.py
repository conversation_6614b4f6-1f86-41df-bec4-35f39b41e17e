from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
import requests
from bs4 import BeautifulSoup
import logging
import json
from datetime import datetime, timezone, timedelta
from concurrent.futures import ThreadPoolExecutor
import asyncio
import bleach
import re
from apscheduler.schedulers.background import BackgroundScheduler
from os import environ
from dotenv import load_dotenv
import os
import time
import random  # إضافة مكتبة random للاختيار العشوائي
from functools import lru_cache
import aiohttp  # إضافة مكتبة aiohttp للطلبات غير المتزامنة
from urllib.parse import quote, urlencode  # لترميز الروابط في وظيفة اختصار الروابط
from typing import Dict, Optional, List, Union
from discudemy_scraper import DiscudemyScraper
from real_discount import RealDiscountScraper

# تحميل المتغيرات البيئية
load_dotenv()

# استخدام المتغيرات البيئية مع قيم افتراضية
TOKEN = "8055516959:AAH39KLCvBHVzVjed9DJXt7naxhsAltNx-w"
CHANNEL_ID = "-1002613463650"  # معرف القناة الرقمي
CHANNEL_USERNAME = "udmePro"  # معرف القناة النصي
CHANNEL_URL = "udmePro"  # رابط القناة للاشتراك

# التحقق من وجود المتغيرات المطلوبة
def check_environment_variables():
    # التحقق من أن المتغيرات الأساسية موجودة
    if not TOKEN or not CHANNEL_ID or not CHANNEL_USERNAME or not CHANNEL_URL:
        raise EnvironmentError("المتغيرات البيئية الأساسية غير موجودة")

    # طباعة معلومات التكوين للتصحيح
    logger.info(f"توكن البوت: {TOKEN}")
    logger.info(f"معرف القناة: {CHANNEL_ID}")
    logger.info(f"اسم المستخدم للقناة: {CHANNEL_USERNAME}")
    logger.info(f"رابط القناة: {CHANNEL_URL}")

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# الإعدادات
ADMIN_IDS = [6719024416, 6292358284,1789531376]  # أضف معرفات المشرفين هنا
VIP_FILE = "vip_users.json"  # ملف تخزين بيانات المستخدمين VIP
LINKJUST_API_TOKEN = "cff76ce9a48500a67d0078c6980017d25c309ac1"  # توكن API لخدمة اختصار الروابط

class VIPManager:
    def __init__(self):
        self.vip_data: Dict = self._load_vip_data()

    def _load_vip_data(self) -> Dict:
        """تحميل بيانات المستخدمين VIP من الملف"""
        try:
            if os.path.exists(VIP_FILE):
                with open(VIP_FILE, "r", encoding="utf-8") as f:
                    return json.load(f)
            # إنشاء ملف فارغ إذا لم يكن موجودًا
            with open(VIP_FILE, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات VIP: {e}")
            return {}

    def _save_vip_data(self) -> bool:
        """حفظ بيانات المستخدمين VIP إلى الملف"""
        try:
            with open(VIP_FILE, "w", encoding="utf-8") as f:
                json.dump(self.vip_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات VIP: {e}")
            return False

    def is_vip(self, user_id: int) -> bool:
        """التحقق من حالة VIP للمستخدم"""
        try:
            # المشرفون هم VIP تلقائيًا
            if is_admin(user_id):
                return True

            user = self.vip_data.get(str(user_id))
            if not user:
                return False

            expiry = datetime.strptime(user["expires_at"], "%Y-%m-%d").date()
            return expiry >= datetime.now().date()
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة VIP للمستخدم {user_id}: {e}")
            return False

    def add_vip(self, user_id: int, name: str, days: int) -> bool:
        """إضافة مستخدم جديد كـ VIP"""
        try:
            joined_at = datetime.now().date()
            expires_at = joined_at + timedelta(days=days)

            self.vip_data[str(user_id)] = {
                "name": name,
                "joined_at": str(joined_at),
                "expires_at": str(expires_at),
                "days_total": days
            }

            return self._save_vip_data()
        except Exception as e:
            logger.error(f"خطأ في إضافة مستخدم VIP {user_id}: {e}")
            return False

    def remove_vip(self, user_id: int) -> bool:
        """حذف مستخدم VIP"""
        try:
            if str(user_id) in self.vip_data:
                del self.vip_data[str(user_id)]
                return self._save_vip_data()
            return False
        except Exception as e:
            logger.error(f"خطأ في حذف مستخدم VIP {user_id}: {e}")
            return False

    def get_vip_info(self, user_id: int) -> Optional[Dict]:
        """الحصول على معلومات VIP للمستخدم"""
        return self.vip_data.get(str(user_id))

    def get_all_vip_users(self) -> Dict:
        """الحصول على جميع مستخدمي VIP"""
        return self.vip_data

# إنشاء نسخة عامة من مدير VIP
vip_manager = VIPManager()

def is_admin(user_id: int) -> bool:
    """التحقق مما إذا كان المستخدم مشرفًا"""
    return user_id in ADMIN_IDS

def shorten_url(long_url: str, user_id: int = None) -> str:
    """
    اختصار الرابط باستخدام LinkJust API.
    المستخدمين VIP يحصلون على الرابط الأصلي.
    المستخدمين العاديين يحصلون على رابط مختصر.

    Args:
        long_url (str): الرابط المراد اختصاره
        user_id (int, optional): معرف المستخدم للتحقق من VIP

    Returns:
        str: الرابط الأصلي للـ VIP، والمختصر لغير الـ VIP
    """
    try:
        # التحقق من صحة الرابط
        if not long_url or not long_url.startswith(('http://', 'https://')):
            logger.warning(f"تنسيق رابط غير صالح: {long_url}")
            return long_url

        # إذا كان المستخدم VIP، إرجاع الرابط الأصلي مباشرة
        if user_id and vip_manager.is_vip(user_id):
            logger.info(f"مستخدم VIP {user_id} - إرجاع الرابط الأصلي")
            return long_url

        # اختصار الرابط باستخدام LinkJust API
        encoded_url = urlencode({'url': long_url})
        api_url = f"https://linkjust.com/api?api={LINKJUST_API_TOKEN}&{encoded_url}"

        response = requests.get(api_url)
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                logger.info(f"تم اختصار الرابط بنجاح: {long_url} -> {result.get('shortenedUrl')}")
                return result.get("shortenedUrl")
            else:
                logger.warning(f"فشل اختصار الرابط: {result.get('message')}")

        # في حالة الفشل، إرجاع الرابط الأصلي
        return long_url
    except Exception as e:
        logger.error(f"خطأ في اختصار الرابط: {e}")
        return long_url

async def admin_only(update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
    """التحقق من صلاحيات المشرف وإرسال رسالة خطأ إذا لم يكن مشرفًا"""
    if not is_admin(update.effective_user.id):
        # التحقق من وجود message قبل الرد
        if update.message:
            await update.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        elif update.callback_query:
            await update.callback_query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return False
    return True

class Config:
    BASE_URL = 'https://comidoc.net'
    DISCUDEMY_URL = 'https://www.discudemy.com/language/arabic'
    REAL_DISCOUNT_URL = 'https://cdn.real.discount/api/courses'
    REQUEST_TIMEOUT = 10
    COURSES_FILE = 'courses.json'
    DISCUDEMY_COURSES_FILE = 'discudemy_courses.json'
    REAL_DISCOUNT_COURSES_FILE = 'real_discount_courses.json'
    SENT_COURSES_FILE = 'sent_courses.json'
    MAX_PAGES = 70  # تقليل عدد الصفحات المعالجة
    DISCUDEMY_MAX_PAGES = 20  # عدد صفحات discudemy للمعالجة
    CONCURRENT_REQUESTS = 10  # عدد الطلبات المتزامنة
    CACHE_TIMEOUT = 3600  # مدة صلاحية التخزين المؤقت بالثواني (ساعة واحدة)

def initialize_files():
    """إنشاء الملفات المطلوبة إذا لم تكن موجودة"""
    files = {
        Config.COURSES_FILE: [],
        Config.DISCUDEMY_COURSES_FILE: [],
        Config.REAL_DISCOUNT_COURSES_FILE: [],
        Config.SENT_COURSES_FILE: [],
    }

    for file_path, default_content in files.items():
        if not os.path.exists(file_path):
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(default_content, f, ensure_ascii=False, indent=4)
                logger.info(f"تم إنشاء ملف {file_path}")
            except Exception as e:
                logger.error(f"خطأ في إنشاء ملف {file_path}: {e}")

# إعداد المجدول
scheduler = BackgroundScheduler({'apscheduler.timezone': 'UTC'})

# إعداد جلسة الطلبات
session = requests.Session()
session.headers.update({'User-Agent': 'Mozilla/5.0 (compatible; CourseBot/1.0)'})

# نظام التخزين المؤقت
cache = {}

def get_cached_data(key, fetch_func, timeout=Config.CACHE_TIMEOUT):
    """الحصول على البيانات من التخزين المؤقت أو جلبها إذا لم تكن موجودة"""
    current_time = time.time()
    if key in cache and current_time - cache[key]['timestamp'] < timeout:
        return cache[key]['data']

    # جلب البيانات
    data = fetch_func()

    # تخزين البيانات في التخزين المؤقت
    cache[key] = {
        'data': data,
        'timestamp': current_time
    }

    return data

# تخزين مؤقت للطلبات HTTP
@lru_cache(maxsize=100)
def cached_request(url, timeout=Config.REQUEST_TIMEOUT):
    """إجراء طلب HTTP مع تخزين مؤقت"""
    response = session.get(url, timeout=timeout)
    response.raise_for_status()
    return response.text

def check_course_coupon(course_url):
    """التحقق من حالة الكوبون في صفحة الدورة"""
    try:
        # التحقق من صحة الرابط
        if not course_url.startswith(('http://', 'https://')):
            return "error"

        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(course_url)

        # تحليل HTML بشكل أكثر كفاءة
        soup = BeautifulSoup(html_content, 'html.parser')

        # البحث عن جميع النصوص المتعلقة بالكوبونات في مرة واحدة
        all_text = ' '.join([element.get_text() for element in soup.select('div, span, p, h1, h2, h3, h4, h5, h6')])
        all_text_upper = all_text.upper()

        # التحقق من الكوبون المجاني
        if '100%OFF COUPON' in all_text_upper or '100% OFF' in all_text_upper:
            return "paid_with_coupon"

        # التحقق من الكوبون المخفض
        if 'COUPON' in all_text_upper and '$' in all_text and not ('100%' in all_text_upper and 'OFF' in all_text_upper):
            return "discounted"

        # التحقق من الخصومات الجزئية
        if re.search(r'\d+%\s*OFF', all_text_upper) and not re.search(r'100%\s*OFF', all_text_upper):
            return "discounted"

        # إذا لم يتم العثور على أي خصم، فالكوبون منتهي الصلاحية
        return "expired"

    except requests.exceptions.RequestException as e:
        logger.error(f"خطأ في الاتصال: {e}")
        return "error"
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        return "error"

def extract_udemy_link_and_coupons_left(course_url):
    """استخراج رابط Udemy مع كود الخصم وعدد الكوبونات المتبقية والسعر الأصلي من صفحة الدورة"""
    try:
        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(course_url)
        soup = BeautifulSoup(html_content, 'html.parser')

        # البحث عن رابط Udemy بشكل أكثر كفاءة
        udemy_link = soup.select_one('a[href*="udemy.com/course"][href*="couponCode"]')

        # البحث عن عدد الكوبونات المتبقية بطريقة أكثر كفاءة
        coupons_left = 0

        # جمع كل النصوص التي تحتوي على "left" أو "remaining"
        left_texts = []
        for element in soup.select('div, span, p, strong'):
            text = element.get_text().lower().strip()
            if 'left' in text or 'remaining' in text:
                left_texts.append(text)

        # البحث عن نمط الكوبونات المتبقية في النصوص المجمعة
        for text in left_texts:
            # تحديد النص الذي يحتوي على "left" وأرقام قبله
            match = re.search(r'(\d+)\s*(?:coupons?|codes?)?(?:\s+left|\s+remaining)', text)
            if match:
                coupons_left = int(match.group(1))
                # التحقق من أن الرقم منطقي (أقل من 10000)
                if coupons_left > 0 and coupons_left < 10000:
                    break

        # إذا لم نجد عدد الكوبونات، نبحث عن أي رقم قريب من كلمة "left"
        if coupons_left == 0:
            for text in left_texts:
                # استخراج جميع الأرقام من النص
                numbers = re.findall(r'\d+', text)
                for num in numbers:
                    if len(num) < 5:  # نتأكد أن الرقم ليس كبيرًا جدًا
                        coupons_left = int(num)
                        if coupons_left > 0:
                            break
                if coupons_left > 0:
                    break

        # البحث عن نص عربي يحتوي على "متبقي" أو "متبقية"
        if coupons_left == 0:
            arabic_texts = []
            for element in soup.select('div, span, p, strong'):
                text = element.get_text().strip()
                if 'متبقي' in text or 'متبقية' in text:
                    arabic_texts.append(text)

            for text in arabic_texts:
                # استخراج الأرقام من النص العربي
                numbers = re.findall(r'\d+', text)
                for num in numbers:
                    if len(num) < 5:
                        coupons_left = int(num)
                        if coupons_left > 0:
                            break
                if coupons_left > 0:
                    break

        # إذا لم نجد عدد الكوبونات، نفترض أن هناك 100 كوبون متبقي
        if coupons_left == 0 or coupons_left > 10000:
            coupons_left = 100

        # استخراج صورة الدورة في نفس الوظيفة
        thumbnail = 'https://via.placeholder.com/150'  # صورة افتراضية
        picture = soup.find('picture')

        if picture:
            img = picture.find('img')
            if img and img.get('src'):
                thumbnail = img['src']
            else:
                source = picture.find('source', type='image/jpeg')
                if source and source.get('srcset'):
                    thumbnail = source['srcset']

        # البحث عن صورة في أماكن أخرى إذا لم نجدها
        if thumbnail == 'https://via.placeholder.com/150':
            img = soup.select_one('img.course-image, img.course-thumbnail, img[alt*="course"], img[alt*="Course"]')
            if img and img.get('src'):
                thumbnail = img['src']

        # استخراج السعر الأصلي للدورة
        original_price = "49.99$"  # سعر افتراضي

        # البحث عن السعر في صفحة الدورة
        price_elements = soup.select('.text-2xl.font-medium.text-white, .text-xl.font-medium.text-white, .text-lg.font-medium.text-white, div[class*="price"], span[class*="price"]')
        for element in price_elements:
            text = element.get_text().strip()
            # البحث عن نمط السعر (مثل $13.99 أو 13.99$)
            price_match = re.search(r'(\$\d+\.\d+|\d+\.\d+\$|\$\d+|\d+\$)', text)
            if price_match:
                original_price = price_match.group(1)
                break

        # إذا لم نجد السعر في العناصر المحددة، نبحث في كل النص
        if original_price == "49.99$":
            for element in soup.select('div, span, p, h1, h2, h3, h4, h5, h6'):
                text = element.get_text().strip()
                price_match = re.search(r'(\$\d+\.\d+|\d+\.\d+\$|\$\d+|\d+\$)', text)
                if price_match:
                    original_price = price_match.group(1)
                    # التأكد من أن السعر منطقي (أكبر من 5 دولارات)
                    price_value = float(re.sub(r'[^\d.]', '', original_price))
                    if price_value > 5:
                        break

        return udemy_link['href'] if udemy_link else None, coupons_left, thumbnail, original_price

    except Exception as e:
        logger.error(f"خطأ في استخراج رابط Udemy: {e}")
        return None, 0, 'https://via.placeholder.com/150', "49.99$"

async def send_course_message(update: Update, course: dict) -> None:
    """إرسال رسالة الدورة بالتنسيق المطلوب"""
    try:
        # تحضير نص الرسالة بالتنسيق الاحترافي
        language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

        # إضافة إيموجي عشوائي للعنوان
        title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
        random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
        random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

        # تحديد سعر الدورة الأصلي من البيانات المستخرجة
        original_price = course.get('original_price', "49.99$")

        # تحديد تاريخ النشر (التاريخ الحالي)
        current_date = datetime.now().strftime("%Y-%m-%d")

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        # اختصار الرابط للمستخدمين غير VIP
        course_link = course['link']
        if not is_vip_user:
            course_link = shorten_url(course_link)

        message = (
            f"{random_emoji1} <b>كورس جديد متاح مجاناً!</b> {random_emoji2}\n\n"
            f"📝 <b>{course['title']}</b>\n\n"
            f"💰 <b>السعر الأصلي:</b> {original_price}\n"
            f"🎁 <b>السعر الآن:</b> مجاني (خصم 100%)\n"
            f"🔑 <b>كوبون:</b> مفعل تلقائي\n"
            f"🏢 <b>المنصة:</b> Udemy\n"
            f"📅 <b>تاريخ النشر:</b> {current_date}\n"
            f"👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', '100')}\n"
            f"🌐 <b>اللغة:</b> {language_flag}\n\n"
        )

        # إضافة معلومات VIP للمستخدمين غير VIP فقط
        if not is_vip_user:
            vip_info = """
💎 <b>ميزة VIP</b> 💎
احصل على روابط مباشرة للدورات بدون اختصار وبدون إعلانات!
"""
            message += vip_info
        else:
            # للمستخدمين VIP
            vip_info = """
💎 <b>أنت عضو VIP!</b> 💎
يمكنك الحصول على روابط مباشرة للدورات بدون إعلانات.
"""
            message += vip_info

        message += f"\n⚡ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡"

        # إنشاء زر "الحصول على الكوبون"
        keyboard = [
            [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)],
            [InlineKeyboardButton("📢 قناة الدورات", url='https://t.me/udmePro')]
        ]

        # إنشاء معرف فريد للدورة باستخدام hash للعنوان
        course_id = str(abs(hash(course['title'])))

        # إضافة زر "إرسال إلى القناة" للمشرفين فقط
        if is_admin(user_id):
            keyboard.append([InlineKeyboardButton("📢 إرسال إلى القناة", callback_data=f'send_channel_{course_id}')])

        if CHANNEL_USERNAME:
            # إضافة زر للقناة
            keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

        # إضافة زر للاشتراك في خدمة VIP للمستخدمين غير VIP
        if not is_vip_user:
            keyboard.append([InlineKeyboardButton("💎 اشترك في خدمة VIP", callback_data="vip_subscribe")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # إرسال الرسالة مع الصورة إذا كانت متوفرة
        if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
            # إرسال الرسالة مع الصورة
            await update.message.reply_photo(
                photo=course['thumbnail'],
                caption=message,
                reply_markup=reply_markup,
                parse_mode='HTML'  # استخدام HTML للتنسيق
            )
        else:
            # إرسال الرسالة بدون صورة
            await update.message.reply_text(
                text=message,
                reply_markup=reply_markup,
                disable_web_page_preview=True,
                parse_mode='HTML'  # استخدام HTML للتنسيق
            )

    except Exception as e:
        logger.error(f"خطأ في إرسال رسالة الدورة: {e}")
        # محاولة إرسال الرسالة بدون صورة في حالة حدوث خطأ
        try:
            # تبسيط الرسالة في حالة الخطأ
            simple_message = (
                f"🎓 {course['title']}\n\n"
                f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
                f"🌐 اللغة: {language_flag}\n"
            )

            await update.message.reply_text(
                text=simple_message,
                reply_markup=reply_markup,
                disable_web_page_preview=True
            )
        except Exception as e2:
            logger.error(f"خطأ في إرسال الرسالة البديلة: {e2}")

async def process_page(page, language='ar'):
    """معالجة صفحة واحدة من الدورات"""
    courses = []
    try:
        url = f'{Config.BASE_URL}/coupons?page={page}'

        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(url)
        soup = BeautifulSoup(html_content, 'html.parser')

        flag_icon = 'ar.svg' if language == 'ar' else 'us.svg'

        # استخدام محدد CSS أكثر دقة
        course_divs = soup.select('div.relative')

        # تسجيل عدد الدورات التي تم العثور عليها
        logger.info(f"تم العثور على {len(course_divs)} دورة في الصفحة {page}")

        for course_div in course_divs:
            # التحقق من لغة الدورة
            if course_div.find('img', src=lambda x: x and flag_icon in x):
                title_elem = course_div.find('h2')
                link_elem = course_div.find('a', href=lambda x: x and '/udemy/' in x)

                if title_elem and link_elem:
                    title = bleach.clean(title_elem.get_text(strip=True))
                    full_link = f"{Config.BASE_URL}{link_elem['href']}"

                    try:
                        # التحقق من حالة الكوبون
                        coupon_status = check_course_coupon(full_link)

                        # فقط متابعة المعالجة إذا كان الكوبون مجاني بالكامل
                        if coupon_status == "paid_with_coupon":
                            # استخراج رابط Udemy وعدد الكوبونات المتبقية والصورة والسعر الأصلي في طلب واحد
                            udemy_link, coupons_left, thumbnail, original_price = extract_udemy_link_and_coupons_left(full_link)

                            # فقط إضافة الدورات التي لديها كوبونات متبقية ورابط Udemy صالح
                            if udemy_link and coupons_left > 0:
                                courses.append({
                                    'title': title,
                                    'link': udemy_link,
                                    'comidoc_link': full_link,
                                    'thumbnail': thumbnail,
                                    'coupons_left': coupons_left,
                                    'original_price': original_price,
                                    'language': language,
                                    'timestamp': datetime.now(timezone.utc).isoformat(),
                                    'status': coupon_status
                                })
                                logger.info(f"تمت إضافة دورة مجانية: {title}")
                        elif coupon_status == "discounted":
                            logger.debug(f"تم تخطي دورة بخصم جزئي: {title}")
                        elif coupon_status == "expired":
                            logger.debug(f"تم تخطي دورة منتهية الصلاحية: {title}")

                    except Exception as e:
                        logger.error(f"خطأ في جلب تفاصيل الدورة {full_link}: {e}")
                        continue

        return courses

    except Exception as e:
        logger.error(f"خطأ في معالجة الصفحة {page}: {e}")
        return []



async def fetch_all_courses(language='ar'):
    """جلب جميع الدورات باستخدام المعالجة المتوازية"""
    # استخدام عدد الصفحات المحدد في الإعدادات
    total_pages = Config.MAX_PAGES
    all_courses = []

    # تسجيل بداية عملية جلب الدورات
    start_time = time.time()
    logger.info(f"بدء جلب الدورات من {total_pages} صفحة...")

    # استخدام asyncio.gather مع تحديد عدد المهام المتزامنة
    # تقسيم المهام إلى مجموعات لتجنب استهلاك الذاكرة
    batch_size = Config.CONCURRENT_REQUESTS
    all_results = []

    for i in range(0, total_pages, batch_size):
        # تحديد نطاق الصفحات للمجموعة الحالية
        batch_pages = range(i + 1, min(i + batch_size + 1, total_pages + 1))
        logger.info(f"معالجة الصفحات من {batch_pages.start} إلى {batch_pages.stop - 1}...")

        # إنشاء مهام للمجموعة الحالية
        tasks = [process_page(page, language) for page in batch_pages]

        # تنفيذ المهام بالتوازي
        batch_results = await asyncio.gather(*tasks)
        all_results.extend(batch_results)

        # تسجيل تقدم العملية
        logger.info(f"تم الانتهاء من معالجة {len(batch_results)} صفحة")

    # تجميع النتائج
    for courses in all_results:
        all_courses.extend(courses)

    # حفظ الدورات في الملف
    try:
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_courses, f, ensure_ascii=False, indent=2)

        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        logger.info(f"تم حفظ {len(all_courses)} دورة بنجاح في {elapsed_time:.2f} ثانية")
    except Exception as e:
        logger.error(f"خطأ في حفظ الدورات: {e}")

    return all_courses

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """رسالة البداية"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # التحقق من حالة VIP للمستخدم
    is_vip_user = vip_manager.is_vip(user_id)
    is_user_admin = is_admin(user_id)

    # إنشاء أزرار متجاورة بدلاً من أزرار متراصة
    # الصف الأول: الدورات العربية
    row1 = [
        InlineKeyboardButton("🇸🇦 الدورات العربية", callback_data='ar_courses')
    ]

    # الصف الثاني: دورات ديسكوديمي
    row2 = [
        InlineKeyboardButton("💰 دورات ديسكوديمي المدفوعة", callback_data='discudemy_courses')
    ]

    # الصف الثالث: دورات Real Discount
    row2_5 = [
        InlineKeyboardButton("🔥 دورات Real Discount المدفوعة", callback_data='real_discount_courses')
    ]

    # الصف الثالث: أزرار التحديث المنفصلة
    row3 = [
        InlineKeyboardButton("🔄 تحديث Comidoc", callback_data='update_comidoc'),
        InlineKeyboardButton("🔄 تحديث Discudemy", callback_data='update_discudemy')
    ]

    # الصف الرابع: زر تحديث Real Discount
    row3_5 = [
        InlineKeyboardButton("🔄 تحديث Real Discount", callback_data='update_real_discount')
    ]

    # الصف الرابع: تحديث جميع الدورات
    row4 = [
        InlineKeyboardButton("🔄 تحديث جميع الدورات", callback_data='update_all_courses')
    ]

    # الصف الخامس: قناة الدورات وزر VIP
    row5 = [
        InlineKeyboardButton("📢 قناة الدورات", url='https://t.me/udmePro')
    ]

    # إضافة زر معلومات VIP للصف الخامس
    if is_vip_user:
        row5.append(InlineKeyboardButton("💎 معلومات VIP", callback_data='vip_info'))
    else:
        row5.append(InlineKeyboardButton("💎 اشترك في VIP", callback_data='vip_subscribe'))

    # إنشاء لوحة المفاتيح مع الصفوف
    keyboard = [row1, row2, row2_5, row3, row3_5, row4, row5]

    # إضافة صف رابع لأزرار المشرف إذا كان المستخدم مشرفًا
    if is_user_admin:
        keyboard.append([InlineKeyboardButton("⚙️ لوحة المشرف", callback_data='admin_panel')])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # قائمة من رسائل الترحيب المتنوعة
    welcome_templates = [
        "مرحباً بك في عالم الكورسات المجانية!",
        "أهلاً بك في بوت كورسات Udemy المجانية!",
        "مرحباً بك في رحلة التعلم المجاني!",
        "أهلاً وسهلاً بك في بوت الكورسات المجانية!",
        "مرحباً بك في بوت الفرص التعليمية المجانية!",
        "أهلاً بك في مركز الكورسات المجانية!",
        "مرحباً بك في بوابة التعلم المجاني!",
        "أهلاً وسهلاً في بوت كورسات Udemy المجانية!",
        "مرحباً بك في عالم المعرفة المجانية!",
        "أهلاً بك في منصة الكورسات المجانية!",
        "أهلاً بك في بوت الكورسات التعليمية المجانية!",
        "مرحباً بك في مركز التعلم الإلكتروني المجاني!",
        "أهلاً وسهلاً بك في عالم الدورات المجانية!",
        "مرحباً بك في بوابة المعرفة المجانية!",
        "أهلاً بك في مجتمع التعلم المجاني!",
        "مرحباً بك في منصة الفرص التعليمية المجانية!",
        "أهلاً بك في رحلة تطوير مهاراتك مجاناً!",
        "مرحباً بك في بوت الكوبونات المجانية لدورات Udemy!",
        "أهلاً بك في مركز الكوبونات المجانية!",
        "مرحباً بك في عالم التعلم الذاتي المجاني!"
    ]

    # قائمة من الرموز التعبيرية المتنوعة للترحيب
    welcome_emojis = ["🌟", "✨", "🚀", "📚", "🔥", "💫", "🎓", "⭐", "🎓", "💡", "🎯", "💻", "📱", "🖥️", "📊", "🎨", "🎬", "🎮", "🎵", "📝", "🏆", "🌠", "🎁", "💎", "🔔", "🎊", "🎉", "🌺", "🌸", "🌼", "⏱", "🌹", "🍀", "🌞", "⚡", "🎖", "🌊", "🌄", "📉", "🖼"]

    # قائمة من الرموز التعبيرية للتعليم والتكنولوجيا
    education_emojis = ["📚", "🎓", "📝", "✏️", "📖", "🔍", "💡", "🧠", "📊", "📈", "🖥️", "💻", "📱", "⌨️", "🖱️", "🎯", "🏆", "🥇", "📋", "📌"]

    # قائمة من الرموز التعبيرية للتحفيز
    motivation_emojis = ["🚀", "⚡", "💪", "🔥", "✨", "🌟", "💯", "🏅", "🎖️", "🏆", "🥇", "⭐", "☀️", "⭐", "💫", "🌠", "🎯", "🧿", "💎", "🌺"]

    import random

    # اختيار رسالة ترحيب عشوائية
    welcome_template = random.choice(welcome_templates)

    # اختيار رموز تعبيرية عشوائية من كل فئة
    welcome_emoji = random.choice(welcome_emojis)
    education_emoji = random.choice(education_emojis)
    motivation_emoji = random.choice(motivation_emojis)

    # اختيار رموز إضافية للتزيين
    decoration_emoji1 = random.choice(welcome_emojis)
    decoration_emoji2 = random.choice(welcome_emojis)

    # إنشاء نمط زخرفي عشوائي للترحيب
    decoration_patterns = [
        f"{decoration_emoji1} {decoration_emoji2} {decoration_emoji1}",
        f"{decoration_emoji1}✧{decoration_emoji2}✧{decoration_emoji1}",
        f"・{decoration_emoji1}・{decoration_emoji2}・{decoration_emoji1}・",
        f"━━━ {decoration_emoji1} ━━━",
        f"⋆｡°✩ {decoration_emoji1} ✩°｡⋆",
        f"★彡 {decoration_emoji1} 彡★",
        f"⊱ {decoration_emoji1} {decoration_emoji2} {decoration_emoji1} ⊰",
        f"❅ {decoration_emoji1} ❅ {decoration_emoji2} ❅",
        f"✿ {decoration_emoji1} ✿",
        f"⋆⭒⋆ {decoration_emoji1} ⋆⭒⋆"
    ]

    decoration = random.choice(decoration_patterns)

    # إنشاء رسالة الترحيب مع الرموز العشوائية
    welcome_formats = [
        f"{decoration}\n\n{welcome_emoji} <b>{welcome_template}</b> {motivation_emoji}\n\n",
        f"{welcome_emoji} <b>{welcome_template}</b> {motivation_emoji}\n{decoration}\n\n",
        f"{decoration}\n{welcome_emoji} <b>{welcome_template}</b> {motivation_emoji}\n{decoration}\n\n",
        f"{welcome_emoji} {motivation_emoji} {welcome_emoji}\n<b>{welcome_template}</b>\n{decoration}\n\n",
        f"{decoration}\n\n{welcome_emoji} <b>{welcome_template}</b>\n\n"
    ]

    welcome_message = random.choice(welcome_formats)

    # إضافة معلومات VIP
    welcome_message += f"{education_emoji} /vip_info - عرض معلومات VIP الخاصة بك\n"

    # إضافة معلومات للمستخدم بشكل ديناميكي
    if is_vip_user:
        # قائمة من رسائل VIP المتنوعة
        vip_messages = [
            "أنت عضو VIP! يمكنك الحصول على روابط مباشرة بدون اختصار.",
            "مرحباً بك عضو VIP المميز! استمتع بالروابط المباشرة بدون إعلانات.",
            "عضويتك VIP تمنحك وصولاً مباشراً للدورات بدون اختصار للروابط.",
            "كعضو VIP، يمكنك الوصول للدورات مباشرة بدون أي إعلانات مزعجة.",
            "استمتع بميزات VIP الحصرية: روابط مباشرة وتجربة خالية من الإعلانات!",
            "عضويتك VIP تمنحك تجربة استخدام متميزة مع روابط مباشرة للدورات."
        ]

        # رموز VIP المتنوعة
        vip_emojis = ["💎", "👑", "🏆", "✨", "⭐", "🌟", "🔮", "🌠", "🎖️", "🏅"]

        # اختيار رسالة ورمز عشوائي
        vip_message = random.choice(vip_messages)
        vip_emoji1 = random.choice(vip_emojis)
        vip_emoji2 = random.choice(vip_emojis)

        # إضافة رسالة VIP بتنسيق جذاب
        welcome_message += f"\n{vip_emoji1} <b>{vip_message}</b> {vip_emoji2}\n"
    else:
        # قائمة من رسائل ترحيبية مشوقة للمستخدمين العاديين
        free_user_messages = [
            "استمتع بآلاف الدورات المجانية من Udemy مباشرة عبر البوت!",
            "اكتشف كنوز المعرفة المجانية واحصل على شهادات معتمدة من Udemy!",
            "طور مهاراتك مجاناً مع أفضل الدورات العربية على Udemy!",
            "فرصتك للتعلم المجاني متاحة الآن! كورسات حصرية بانتظارك!",
            "كل يوم كورسات جديدة مجانية! لا تفوت الفرصة للتعلم والتطور!",
            "نقدم لك أفضل الكورسات المجانية يومياً لتطوير مهاراتك!",
            "تعلم ما تريد مجاناً! آلاف الكورسات بانتظارك!",
            "فرصة ذهبية للتعلم! كورسات مدفوعة متاحة مجاناً لفترة محدودة!",
            "استثمر في نفسك مجاناً! كورسات احترافية بشهادات معتمدة!",
            "كن الأول في الحصول على الكورسات المجانية قبل نفاد الكوبونات!"
        ]

        # رموز تحفيزية متنوعة
        motivation_special_emojis = ["🚀", "⚡", "🔥", "💯", "✅", "🎯", "🏆", "🌟", "💪", "🎁", "🎊", "🎉"]

        # اختيار رسالة ورموز عشوائية
        free_message = random.choice(free_user_messages)
        free_emoji1 = random.choice(motivation_special_emojis)
        free_emoji2 = random.choice(motivation_special_emojis)

        # إضافة رسالة تحفيزية للمستخدم العادي بتنسيق جذاب
        welcome_message += f"\n{free_emoji1} <b>{free_message}</b> {free_emoji2}\n"

        # إضافة معلومات عن مميزات البوت للمستخدمين العاديين بشكل أكثر تنوعاً
        # قائمة موسعة من مميزات البوت
        bot_features_extended = [
            "✓ كورسات عربية مجانية 100%",
            "✓ تحديثات يومية بأحدث الكورسات",
            "✓ كوبونات تفعل تلقائياً",
            "✓ شهادات معتمدة من Udemy",
            "✓ محتوى تعليمي متنوع",
            "✓ واجهة سهلة الاستخدام",
            "✓ تنبيهات فورية بالكورسات الجديدة",
            "✓ دعم فني متواصل",
            "✓ مجتمع تعليمي نشط",
            "✓ تصنيفات متعددة للكورسات",
            "✓ تقييمات حقيقية للكورسات",
            "✓ محتوى محدث باستمرار",
            "✓ إمكانية حفظ الكورسات المفضلة",
            "✓ تصفية الكورسات حسب التخصص"
        ]

        # اختيار رموز مميزة عشوائية
        feature_icons = ["🎓", "📚", "🔍", "⭐", "🌟", "💫", "🎯", "🚀", "💻", "📱", "🖥️", "📊", "📈", "🔔", "🎁", "🏆"]
        feature_icon = random.choice(feature_icons)

        # اختيار عدد عشوائي من المميزات (4-6) من القائمة الموسعة
        selected_features = random.sample(bot_features_extended, random.randint(4, 6))

        # إضافة تنسيق عشوائي للعنوان
        feature_title_formats = [
            f"<b>{feature_icon} مميزات البوت:</b>",
            f"<b>{feature_icon} ما يميز البوت:</b>",
            f"<b>{feature_icon} لماذا تستخدم البوت:</b>",
            f"<b>{feature_icon} مزايا البوت:</b>",
            f"<b>{feature_icon} ما يقدمه البوت:</b>"
        ]
        feature_title = random.choice(feature_title_formats)

        # إضافة المميزات بتنسيق جذاب مع تنويع الرموز
        feature_list = []
        for feature in selected_features:
            # استخدام رمز عشوائي لكل ميزة
            icon = random.choice(["✓", "✅", "☑️", "✔️"])
            # استبدال الرمز الحالي بالرمز العشوائي
            feature_with_icon = feature.replace("✓", icon)
            feature_list.append(feature_with_icon)

        # إضافة المميزات للرسالة
        welcome_message += f"\n{feature_title}\n" + "\n".join(feature_list) + "\n"

    # إضافة أوامر المشرف إذا كان المستخدم مشرفًا
    if is_user_admin:
        # رموز المشرف المتنوعة
        admin_emojis = ["🔑", "⚙️", "🛠️", "🔧", "👨‍💻", "👩‍💻", "🔐", "🔒", "🔓", "📊"]

        # اختيار رموز عشوائية
        admin_emoji1 = random.choice(admin_emojis)
        admin_emoji2 = random.choice(admin_emojis)
        admin_emoji3 = random.choice(admin_emojis)

        # إنشاء قسم أوامر المشرف بتنسيق جذاب
        admin_commands = f"""
{admin_emoji1} <b>أوامر المشرف:</b> {admin_emoji2}
{admin_emoji3} /add_vip - إضافة عضو VIP (المعرف، المدة، الاسم)
{admin_emoji3} /send_to_channel - إرسال الدورات للقناة
{admin_emoji3} /clean_courses - تنظيف الكوبونات المنتهية
"""
        welcome_message += f"\n{admin_commands}\n"

    # قائمة من رسائل الختام المتنوعة
    closing_messages = [
        "اختر من القائمة أدناه:",
        "يمكنك اختيار أحد الخيارات أدناه:",
        "استكشف الخيارات المتاحة أدناه:",
        "اختر ما يناسبك من القائمة التالية:",
        "تصفح الخيارات المتاحة من خلال الأزرار أدناه:"
    ]

    # اختيار رسالة ختامية عشوائية
    closing_message = random.choice(closing_messages)
    welcome_message += f"\n{closing_message}"

    # صورة الترحيب
    welcome_image = "https://g.top4top.io/p_33930bt4w1.jpg"

    try:
        # إرسال الرسالة مع الصورة
        await update.message.reply_photo(
            photo=welcome_image,
            caption=welcome_message,
            reply_markup=reply_markup,
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال صورة الترحيب: {e}")
        # في حالة فشل إرسال الصورة، نرسل رسالة نصية فقط
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

async def show_courses(update: Update, context: ContextTypes.DEFAULT_TYPE, filters=None):
    """عرض الدورات مع التصفية"""
    query = update.callback_query
    courses = []

    try:
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            all_courses = json.load(f)

        if filters:
            courses = [c for c in all_courses if all(c.get(k) == v for k, v in filters.items())]
        else:
            courses = all_courses

        # عكس ترتيب الدورات من الأسفل إلى الأعلى
        courses = list(reversed(courses))

        if not courses:
            await query.answer("لا توجد دورات متوفرة حالياً")
            return

        # إضافة رسالة توضيحية عن ترتيب الدورات
        await query.message.reply_text("تم عرض الدورات بترتيب عكسي (من الأسفل إلى الأعلى)")

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        for course in courses:
            # إضافة إيموجي عشوائي للعنوان
            title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
            random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
            random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

            # تحديد سعر الدورة الأصلي من البيانات المستخرجة
            original_price = course.get('original_price', "49.99$")

            # تحديد العلم حسب اللغة
            language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

            # تحديد تاريخ النشر (التاريخ الحالي)
            current_date = datetime.now().strftime("%Y-%m-%d")

            # اختصار الرابط للمستخدمين غير VIP
            course_link = course['link']
            if not is_vip_user:
                course_link = shorten_url(course_link)

            message = (
                f"{random_emoji1} <b>كورس جديد متاح مجاناً!</b> {random_emoji2}\n\n"
                f"📝 <b>{course['title']}</b>\n\n"
                f"💰 <b>السعر الأصلي:</b> {original_price}\n"
                f"🎁 <b>السعر الآن:</b> مجاني (خصم 100%)\n"
                f"🔑 <b>كوبون:</b> مفعل تلقائي\n"
                f"🏢 <b>المنصة:</b> Udemy\n"
                f"📅 <b>تاريخ النشر:</b> {current_date}\n"
                f"👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', '100')}\n"
                f"🌐 <b>اللغة:</b> {language_flag}\n\n"
            )

            # إضافة معلومات VIP للمستخدمين غير VIP فقط
            if not is_vip_user:
                vip_info = """
💎 <b>ميزة VIP</b> 💎
احصل على روابط مباشرة للدورات بدون اختصار وبدون إعلانات!
"""
                message += vip_info
            else:
                # للمستخدمين VIP
                vip_info = """
💎 <b>أنت عضو VIP!</b> 💎
يمكنك الحصول على روابط مباشرة للدورات بدون إعلانات.
"""
                message += vip_info

            message += f"\n⚡ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡"

            # إنشاء معرف فريد للدورة باستخدام hash للعنوان
            # استخدام رقم موجب دائمًا للمعرف
            course_id = str(abs(hash(course['title'])))
            logger.debug(f"إنشاء معرف للدورة: {course['title']} -> {course_id}")

            # إنشاء أزرار متجاورة بدلاً من أزرار متراصة
            # الصف الأول: زر الحصول على الكوبون (يبقى وحده لأهميته)
            row1 = [
                InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)
            ]

            # الصف الثاني: زر القناة وزر VIP
            row2 = []

            if CHANNEL_USERNAME:
                row2.append(InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}"))

            # إضافة زر للاشتراك في خدمة VIP للمستخدمين غير VIP
            if not is_vip_user:
                row2.append(InlineKeyboardButton("💎 اشترك في VIP", callback_data="vip_subscribe"))

            # إنشاء لوحة المفاتيح مع الصفوف
            keyboard = [row1]

            # إضافة الصف الثاني إذا كان يحتوي على أزرار
            if row2:
                keyboard.append(row2)

            # إضافة زر "إرسال إلى القناة" للمشرفين فقط
            if is_admin(user_id):
                keyboard.append([InlineKeyboardButton("📢 إرسال إلى القناة", callback_data=f'send_channel_{course_id}')])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # إرسال الرسالة مع الصورة إذا كانت متوفرة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                try:
                    # إرسال الرسالة مع الصورة
                    await query.message.reply_photo(
                        photo=course['thumbnail'],
                        caption=message,
                        parse_mode='HTML',
                        reply_markup=reply_markup
                    )
                except Exception as e:
                    logger.error(f"خطأ في إرسال الصورة: {e}")
                    # إرسال بدون صورة في حالة الخطأ
                    await query.message.reply_text(
                        message,
                        parse_mode='HTML',
                        reply_markup=reply_markup,
                        disable_web_page_preview=True
                    )
            else:
                # إرسال الرسالة بدون صورة
                await query.message.reply_text(
                    message,
                    parse_mode='HTML',
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )

    except FileNotFoundError:
        await query.answer("لم يتم العثور على قاعدة البيانات")
        logger.error("ملف الدورات غير موجود")
    except Exception as e:
        await query.answer("حدث خطأ أثناء جلب الدورات")
        logger.error(f"خطأ في عرض الدورات: {e}")

async def clear_cache(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تنظيف الذاكرة المؤقتة"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري تنظيف الذاكرة المؤقتة...")

    try:
        # تنظيف ذاكرة التخزين المؤقت
        cache.clear()
        cached_request.cache_clear()

        # تسجيل نجاح العملية
        logger.info("تم تنظيف الذاكرة المؤقتة بنجاح")
        await message.edit_text("✅ تم تنظيف الذاكرة المؤقتة بنجاح")

    except Exception as e:
        logger.error(f"خطأ في تنظيف الذاكرة المؤقتة: {e}")
        await message.edit_text("❌ حدث خطأ في تنظيف الذاكرة المؤقتة")

async def verify_coupons_manually(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """التحقق اليدوي من صلاحية الكوبونات"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري التحقق من صلاحية الكوبونات...")

    try:
        # استدعاء وظيفة التحقق من الكوبونات
        verify_coupons_job()

        # قراءة الدورات المحدثة
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        # عرض نتائج التحقق
        await message.edit_text(
            f"✅ تم التحقق من صلاحية الكوبونات بنجاح!\n"
            f"عدد الدورات النشطة: {len(courses)}\n"
            f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    except Exception as e:
        logger.error(f"خطأ في التحقق اليدوي من صلاحية الكوبونات: {e}")
        await message.edit_text("❌ حدث خطأ في التحقق من صلاحية الكوبونات")

async def send_to_channel_direct(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إرسال الدورة مباشرة إلى القناة"""
    query = update.callback_query

    try:
        # إرسال رسالة للمستخدم
        status_message = await query.message.reply_text("جاري إرسال الدورة إلى القناة...")

        # استخراج معرف الدورة من البيانات
        # التنسيق: send_to_channel_COURSE_ID
        if query.data.startswith('send_to_channel_'):
            course_id = query.data.replace('send_to_channel_', '')
        else:
            parts = query.data.split('_')
            course_id = parts[2] if len(parts) >= 3 else parts[1]

        logger.info(f"معرف الدورة المطلوب إرسالها: {course_id}")
        logger.info(f"بيانات الاستعلام الأصلية: {query.data}")

        # التحقق من صحة معرف الدورة
        if not course_id or course_id == "channel":
            await status_message.edit_text("❌ معرف الدورة غير صالح")
            return

        # قراءة الدورات من ملف comidoc
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                comidoc_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            comidoc_courses = []

        # قراءة الدورات من ملف discudemy
        try:
            with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
                discudemy_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            discudemy_courses = []

        # قراءة الدورات من ملف real.discount
        try:
            with open(Config.REAL_DISCOUNT_COURSES_FILE, 'r', encoding='utf-8') as f:
                real_discount_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            real_discount_courses = []

        # دمج جميع الدورات
        all_courses = comidoc_courses + discudemy_courses + real_discount_courses
        logger.info(f"تم تحميل {len(comidoc_courses)} دورة من comidoc و {len(discudemy_courses)} دورة من discudemy و {len(real_discount_courses)} دورة من real.discount للبحث")

        # البحث عن الدورة بالمعرف
        course = None
        logger.info(f"بدء البحث عن الدورة بالمعرف: {course_id}")

        for i, c in enumerate(all_courses):
            # استخدام abs(hash()) لضمان الحصول على قيمة موجبة متطابقة مع المعرف المستخدم في الزر
            current_hash = str(abs(hash(c['title'])))

            # طباعة تفاصيل البحث لأول 5 دورات
            if i < 5:
                logger.info(f"مقارنة {current_hash} مع {course_id} للدورة: {c['title'][:50]}...")

            if current_hash == course_id:
                course = c
                # تحديد مصدر الدورة
                if c in real_discount_courses:
                    source = 'real.discount'
                elif c in discudemy_courses:
                    source = 'discudemy'
                else:
                    source = 'comidoc'
                logger.info(f"✅ تم العثور على الدورة في {source}: {c['title']}")
                break

        if not course:
            logger.error(f"❌ لم يتم العثور على الدورة بالمعرف: {course_id}")
            logger.error(f"إجمالي الدورات المتاحة: {len(all_courses)}")
            logger.error(f"عينة من معرفات الدورات: {[str(abs(hash(c['title']))) for c in all_courses[:3]]}")
            await status_message.edit_text("❌ لم يتم العثور على الدورة")
            return

        logger.info(f"تم العثور على الدورة: {course['title']}")

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # التحقق مما إذا كانت الدورة قد تم إرسالها بالفعل
        if any(c.get('link') == course['link'] for c in sent_courses):
            logger.info(f"الدورة تم إرسالها بالفعل: {course['title']}")
            await status_message.edit_text("⚠️ تم إرسال هذه الدورة بالفعل إلى القناة")
            return

        # إعداد رسالة الإرسال
        await status_message.edit_text("⏳ جاري إرسال الدورة إلى القناة...")

        # تحضير نص الرسالة بالتنسيق الاحترافي
        language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

        # إضافة إيموجي عشوائي للعنوان
        title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
        random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
        random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

        # تحديد سعر الدورة الأصلي من البيانات المستخرجة
        original_price = course.get('original_price', "49.99$")

        # تحديد تاريخ النشر (التاريخ الحالي)
        current_date = datetime.now().strftime("%Y-%m-%d")

        # اختصار الرابط للقناة (دائمًا نستخدم الرابط المختصر للقناة)
        course_link = shorten_url(course['link'])
        
        # إنشاء رابط المعاينة من الرابط الأصلي
        preview_url = course['link'].replace('/coupon/', '/').split('?')[0]

        message_text = (
            f"{random_emoji1} <b>كورس جديد متاح مجاناً!</b> {random_emoji2}\n\n"
            f"📝 <b>{course['title']}</b>\n\n"
            f"💰 <b>السعر الأصلي:</b> {original_price}\n"
            f"🎁 <b>السعر الآن:</b> مجاني (خصم 100%)\n"
            f"🔑 <b>كوبون:</b> مفعل تلقائي\n"
            f"🏢 <b>المنصة:</b> Udemy\n"
            f"📅 <b>تاريخ النشر:</b> {current_date}\n"
            f"👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', '100')}\n"
            f"🌐 <b>اللغة:</b> {language_flag}\n\n"
            f"💎 <b>للحصول على روابط مباشرة بدون اختصار، اشترك في خدمة VIP!</b>\n\n"
            f"⚡ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡"
        )

        # إنشاء أزرار متجاورة
        # الصف الأول: زر الحصول على الكوبون (يبقى وحده لأهميته)
        row1 = [
        InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link),
        InlineKeyboardButton("👁️ معاينة الدورة", url=preview_url) 
    ]

        # الصف الثاني: زر القناة وزر VIP
        row2 = []

        if CHANNEL_USERNAME:
            row2.append(InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}"))

        # إضافة زر للاشتراك في خدمة VIP
        row2.append(InlineKeyboardButton("💎 اشترك في VIP", url="https://t.me/CiH99x"))

        # إنشاء لوحة المفاتيح مع الصفوف
        keyboard = [row1, row2]
        
        reply_markup = InlineKeyboardMarkup(keyboard)

        # طباعة معلومات التصحيح
        logger.info(f"محاولة إرسال دورة إلى القناة: {course['title']}")

        # التحقق من صلاحيات البوت في القناة
        try:
            # استخدام معرف القناة الصحيح
            channel_id = -1002613463650  # معرف القناة الصحيح

            # محاولة إرسال رسالة اختبار للتحقق من الصلاحيات
            try:
                chat_info = await context.bot.get_chat(channel_id)
                logger.info(f"معلومات القناة: {chat_info.title} (ID: {chat_info.id})")
            except Exception as e:
                logger.warning(f"لم يتمكن من الحصول على معلومات القناة: {e}")

            # إرسال الرسالة إلى القناة مع الصورة إذا كانت متوفرة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                # إرسال الرسالة مع الصورة
                await context.bot.send_photo(
                    chat_id=channel_id,  # معرف القناة
                    photo=course['thumbnail'],
                    caption=message_text,
                    reply_markup=reply_markup,
                    parse_mode='HTML'  # استخدام HTML للتنسيق الغامق
                )
                logger.info("تم إرسال الرسالة مع الصورة")
            else:
                # إرسال الرسالة بدون صورة
                await context.bot.send_message(
                    chat_id=channel_id,  # معرف القناة
                    text=message_text,
                    reply_markup=reply_markup,
                    disable_web_page_preview=True,
                    parse_mode='HTML'  # استخدام HTML للتنسيق الغامق
                )
                logger.info("تم إرسال الرسالة بدون صورة")
        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة إلى القناة: {e}")
            # محاولة إرسال الرسالة بطريقة أخرى
            try:
                channel_id = -1002613463650  # معرف القناة الصحيح
                await context.bot.send_message(
                    chat_id=channel_id,  # معرف القناة
                    text=f"كورس جديد: {course['title']}\n\nالرابط: {course_link}",
                    disable_web_page_preview=True
                )
                logger.info("تم إرسال رسالة مبسطة")
            except Exception as e2:
                logger.error(f"خطأ في إرسال الرسالة المبسطة: {e2}")
                # عرض معلومات مفصلة عن الخطأ
                error_details = f"نوع الخطأ: {type(e2).__name__}\nرسالة الخطأ: {str(e2)}"
                logger.error(f"تفاصيل الخطأ: {error_details}")
                await status_message.edit_text(f"❌ فشل إرسال الرسالة: {str(e2)}")
                return

        # إضافة الدورة إلى قائمة الدورات المرسلة
        course['sent_date'] = datetime.now(timezone.utc).isoformat()
        sent_courses.append(course)

        # حفظ الدورات المرسلة
        with open(Config.SENT_COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(sent_courses, f, ensure_ascii=False, indent=2)

        # إرسال رسالة تأكيد للمستخدم
        await status_message.edit_text(f"✅ تم إرسال الدورة بنجاح إلى القناة")
        logger.info(f"تم إرسال الدورة بنجاح: {course['title']}")

        # إضافة زر للانتقال إلى القناة (استخدم رابط القناة الصحيح إذا كان متاحًا)
        channel_keyboard = [[InlineKeyboardButton("🔔 الانتقال إلى القناة", url="https://t.me/udmePro")]]
        await query.message.reply_text(
            "✅ تم إرسال الدورة بنجاح!\nيمكنك الانتقال إلى القناة لمشاهدة الدورة.",
            reply_markup=InlineKeyboardMarkup(channel_keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في إرسال الدورة إلى القناة: {e}")
        await status_message.edit_text(f"❌ حدث خطأ في إرسال الدورة إلى القناة: {str(e)}")

        # إرسال تفاصيل الخطأ للتصحيح
        error_details = f"نوع الخطأ: {type(e).__name__}\nرسالة الخطأ: {str(e)}\nالدورة: {course['title'] if course else 'غير معروف'}"
        logger.error(f"تفاصيل الخطأ: {error_details}")

async def show_discudemy_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض دورات ديسكوديمي المدفوعة"""
    query = update.callback_query

    # إرسال رسالة انتظار
    message = await query.message.reply_text("جاري تحميل دورات ديسكوديمي المدفوعة...")

    try:
        # جلب دورات ديسكوديمي
        courses = await get_discudemy_courses()

        if not courses:
            await message.edit_text("❌ لم يتم العثور على دورات متاحة من ديسكوديمي")
            return

        # إرسال رسالة توضيحية
        await message.edit_text(f"تم العثور على {len(courses)} دورة مدفوعة من ديسكوديمي (السعر بين 1$ و 999$)")

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        # عرض الدورات (بحد أقصى 10 دورات)
        for course in courses[:10]:
            # إضافة إيموجي عشوائي للعنوان
            title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
            random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
            random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

            # تحديد سعر الدورة الأصلي من البيانات المستخرجة
            original_price = course.get('original_price', "Unknown")

            # تحديد العلم حسب اللغة
            language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

            # تحديد تاريخ النشر (التاريخ الحالي)
            current_date = datetime.now().strftime("%Y-%m-%d")

            # اختصار الرابط للمستخدمين غير VIP
            course_link = course['link']
            if not is_vip_user:
                course_link = shorten_url(course_link, user_id)

            message_text = (
                f"{random_emoji1} <b>كورس مدفوع متاح بسعر مخفض!</b> {random_emoji2}\n\n"
                f"📝 <b>{course['title']}</b>\n\n"
                f"💰 <b>السعر الأصلي:</b> {original_price}\n"
                f"🎁 <b>السعر الآن:</b> مخفض\n"
                f"🔑 <b>كوبون:</b> مفعل تلقائي\n"
                f"🏢 <b>المنصة:</b> Udemy\n"
                f"📅 <b>تاريخ النشر:</b> {current_date}\n"
                f"👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', '100')}\n"
                f"🌐 <b>اللغة:</b> {language_flag}\n\n"
            )

            # إضافة معلومات VIP للمستخدمين غير VIP فقط
            if not is_vip_user:
                vip_info = """
💎 <b>ميزة VIP</b> 💎
احصل على روابط مباشرة للدورات بدون اختصار وبدون إعلانات!
"""
                message_text += vip_info
            else:
                # للمستخدمين VIP
                vip_info = """
💎 <b>أنت عضو VIP!</b> 💎
يمكنك الحصول على روابط مباشرة للدورات بدون إعلانات.
"""
                message_text += vip_info

            message_text += f"\n⚡ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡"

            # إنشاء معرف فريد للدورة باستخدام hash للعنوان
            course_id = str(abs(hash(course['title'])))

            # إنشاء أزرار متجاورة
            # الصف الأول: زر الحصول على الكوبون (يبقى وحده لأهميته)
            row1 = [
                InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)
            ]

            # الصف الثاني: زر القناة وزر VIP
            row2 = []

            if CHANNEL_USERNAME:
                row2.append(InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}"))

            # إضافة زر للاشتراك في خدمة VIP للمستخدمين غير VIP
            if not is_vip_user:
                row2.append(InlineKeyboardButton("💎 اشترك في VIP", callback_data="vip_subscribe"))

            # إنشاء لوحة المفاتيح مع الصفوف
            keyboard = [row1]

            # إضافة الصف الثاني إذا كان يحتوي على أزرار
            if row2:
                keyboard.append(row2)

            # إضافة زر "إرسال إلى القناة" للمشرفين فقط
            if is_admin(user_id):
                keyboard.append([InlineKeyboardButton("📢 إرسال إلى القناة", callback_data=f'send_channel_{course_id}')])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # إرسال الرسالة مع الصورة إذا كانت متوفرة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                try:
                    # إرسال الرسالة مع الصورة
                    await query.message.reply_photo(
                        photo=course['thumbnail'],
                        caption=message_text,
                        parse_mode='HTML',
                        reply_markup=reply_markup
                    )
                except Exception as e:
                    logger.error(f"خطأ في إرسال الصورة: {e}")
                    # إرسال بدون صورة في حالة الخطأ
                    await query.message.reply_text(
                        message_text,
                        parse_mode='HTML',
                        reply_markup=reply_markup,
                        disable_web_page_preview=True
                    )
            else:
                # إرسال الرسالة بدون صورة
                await query.message.reply_text(
                    message_text,
                    parse_mode='HTML',
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )

    except Exception as e:
        logger.error(f"خطأ في عرض دورات ديسكوديمي: {e}")
        await message.edit_text("❌ حدث خطأ أثناء جلب دورات ديسكوديمي")

async def show_real_discount_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض دورات Real Discount المدفوعة"""
    query = update.callback_query

    # إرسال رسالة تحميل
    message = await query.message.reply_text("🔄 جاري تحميل دورات Real Discount...")

    try:
        # جلب دورات Real Discount
        courses = await get_real_discount_courses()

        if not courses:
            await message.edit_text("❌ لم يتم العثور على دورات متاحة من Real Discount")
            return

        # إرسال رسالة توضيحية
        await message.edit_text(f"تم العثور على {len(courses)} دورة مدفوعة من Real Discount (الصفحة الأولى فقط)")

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        # عرض الدورات
        for i, course in enumerate(courses):
            try:
                # إعداد رسالة الدورة بنفس نمط Comidoc و Discudemy

                # حساب نسبة الخصم والأسعار الحقيقية
                original_price_str = course.get('original_price', 'غير محدد')
                current_price_str = course.get('current_price', 'مجاني')

                # حساب نسبة الخصم
                discount_percentage = course.get('discount_percentage', 0)
                if not discount_percentage:
                    try:
                        original = float(str(original_price_str).replace('$', '').replace('مجاني', '0'))
                        current = float(str(current_price_str).replace('$', '').replace('مجاني', '0'))
                        if original > 0:
                            discount_percentage = int(((original - current) / original) * 100)
                    except:
                        discount_percentage = 100

                # تحديد نص السعر الحالي
                if current_price_str == 'مجاني' or course.get('price_value', 0) == 0:
                    if discount_percentage > 0 and original_price_str != 'مجاني':
                        current_price_display = f"مجاني (بدلاً من {original_price_str})"
                    else:
                        current_price_display = "مجاني"
                else:
                    current_price_display = current_price_str

                # إعداد رسالة بنفس تنسيق Comidoc/Discudemy
                course_message = f"""
📚 <b>كورس جديد متاح مجاناً!</b> 📱

📝 <b>[AR] {course.get('title', 'عنوان غير متوفر')}</b>

💰 <b>السعر الأصلي:</b> {original_price_str}
🎁 <b>السعر الآن:</b> {current_price_display}
🔑 <b>كوبون:</b> {'مفعل تلقائي' if course.get('coupon_code') else 'غير متوفر'}
🏢 <b>المنصة:</b> Udemy
📅 <b>تاريخ النشر:</b> {datetime.now().strftime('%d-%m-%Y')}
👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', 100)}
🌐 <b>اللغة:</b> 🇸🇦
"""

                # إضافة رسالة VIP والتحفيز بنفس التنسيق
                if is_vip_user:
                    course_message += "\n\n💎 <b>أنت عضو VIP!</b> 💎\nيمكنك الحصول على روابط مباشرة للدورات بدون إعلانات."

                # إضافة رسالة التحفيز
                course_message += "\n\n⚡️ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡️"

                # إعداد أزرار الدورة
                keyboard = []

                # زر الحصول على الكوبون
                if course.get('link'):
                    keyboard.append([InlineKeyboardButton("⚡️ الحصول على الكوبون ⚡️", url=course['link'])])

                # زر الاشتراك في القناة
                keyboard.append([InlineKeyboardButton("📺 اشترك في القناة", url=f'https://t.me/{CHANNEL_USERNAME}')])

                # زر إرسال إلى القناة (للمشرفين فقط)
                if is_admin(user_id):
                    course_id = str(abs(hash(course['title'])))
                    keyboard.append([InlineKeyboardButton("📤 إرسال إلى القناة", callback_data=f'send_to_channel_{course_id}')])

                reply_markup = InlineKeyboardMarkup(keyboard)

                # إرسال الدورة مع الصورة
                try:
                    if course.get('thumbnail'):
                        await query.message.reply_photo(
                            photo=course['thumbnail'],
                            caption=course_message,
                            reply_markup=reply_markup,
                            parse_mode='HTML'
                        )
                    else:
                        await query.message.reply_text(
                            course_message,
                            reply_markup=reply_markup,
                            parse_mode='HTML'
                        )
                except Exception as photo_error:
                    logger.error(f"خطأ في إرسال الصورة: {photo_error}")
                    # إرسال بدون صورة في حالة الخطأ
                    await query.message.reply_text(
                        course_message,
                        reply_markup=reply_markup,
                        parse_mode='HTML'
                    )

                # توقف قصير بين الرسائل
                await asyncio.sleep(0.5)

                # إيقاف عرض الدورات لغير VIP بعد 3 دورات
                if not is_vip_user and i >= 2:  # عرض 3 دورات فقط
                    # إضافة رسالة ترقية VIP
                    vip_message = """
💎 <b>لعرض المزيد من الدورات، اشترك في VIP!</b> 💎
احصل على روابط مباشرة بدون إعلانات وعرض غير محدود للدورات!
"""
                    await query.message.reply_text(vip_message, parse_mode='HTML')
                    break

            except Exception as course_error:
                logger.error(f"خطأ في عرض دورة Real Discount: {course_error}")
                continue

    except Exception as e:
        logger.error(f"خطأ في عرض دورات Real Discount: {e}")
        await message.edit_text("❌ حدث خطأ أثناء جلب دورات Real Discount")

async def button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة الأزرار"""
    query = update.callback_query
    await query.answer()  # للرد على الضغطة

    # التحقق من صلاحيات المشرف للأوامر المقيدة
    admin_commands = ['update_courses', 'admin_panel', 'admin_stats', 'publish_channel', 'clean_expired', 'clear_cache', 'verify_coupons', 'manage_vip', 'send_message_channel', 'add_new_course']
    if query.data in admin_commands and update.effective_user.id not in ADMIN_IDS:
        await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    # التحقق مما إذا كان الأمر هو إرسال دورة إلى القناة
    if query.data.startswith('send_channel_') or query.data.startswith('send_to_channel_'):
        # طباعة البيانات المرسلة للتصحيح
        logger.info(f"بيانات الزر: {query.data}")

        # استدعاء وظيفة إرسال الدورة إلى القناة مباشرة
        await send_to_channel_direct(update, context)
        return

    # معالجة طلب الاشتراك في خدمة VIP
    elif query.data == 'vip_subscribe':
        await handle_vip_subscribe(update, context)
        return

    # معالجة طلب عرض معلومات VIP
    elif query.data == 'vip_info':
        await handle_vip_info(update, context)
        return

    # معالجة طلب عرض دورات ديسكوديمي
    elif query.data == 'discudemy_courses':
        await show_discudemy_courses(update, context)
        return
    elif query.data == 'real_discount_courses':
        await show_real_discount_courses(update, context)
        return

    # معالجة الأوامر
    filters = None
    if query.data == 'ar_courses':
        filters = {'language': 'ar'}
    elif query.data == 'update_comidoc':
        await update_comidoc_courses(update, context)
        return
    elif query.data == 'update_discudemy':
        await update_discudemy_courses(update, context)
        return
    elif query.data == 'update_real_discount':
        await update_real_discount_courses(update, context)
        return
    elif query.data == 'update_all_courses':
        await update_all_courses(update, context)
        return
    elif query.data == 'admin_panel':
        await show_admin_panel(update, context)
        return
    elif query.data == 'publish_channel':
        await publish_to_channel(update, context)
        return
    elif query.data == 'admin_stats':
        await show_stats(update, context)
        return
    elif query.data == 'clean_expired':
        await clean_expired_courses(update, context)
        return
    elif query.data == 'clear_cache':
        await clear_cache(update, context)
        return
    elif query.data == 'verify_coupons':
        await verify_coupons_manually(update, context)
        return
    elif query.data == 'manage_vip':
        await show_vip_management(update, context)
        return
    elif query.data == 'send_message_channel':
        await show_send_message_form(update, context)
        return
    elif query.data == 'add_new_course':
        await show_add_course_form(update, context)
        return

    if filters is not None:
        await show_courses(update, context, filters)

async def update_comidoc_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Comidoc فقط"""
    message = await update.callback_query.message.reply_text("🔄 جاري تحديث دورات Comidoc...")

    try:
        # جلب الدورات العربية من Comidoc
        ar_courses = await fetch_all_courses('ar')

        # عرض نتائج التحديث
        await message.edit_text(
            f"✅ تم تحديث دورات Comidoc بنجاح!\n"
            f"🇸🇦 دورات عربية: {len(ar_courses)}\n"
            f"🕰️ وقت التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Comidoc: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث دورات Comidoc")

async def update_discudemy_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Discudemy فقط"""
    message = await update.callback_query.message.reply_text("🔄 جاري تحديث دورات Discudemy...")

    try:
        # جلب دورات ديسكوديمي
        discudemy_courses = await fetch_discudemy_courses()

        # عرض نتائج التحديث
        await message.edit_text(
            f"✅ تم تحديث دورات Discudemy بنجاح!\n"
            f"💰 دورات مدفوعة: {len(discudemy_courses)}\n"
            f"🕰️ وقت التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Discudemy: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث دورات Discudemy")

async def update_real_discount_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Real Discount فقط"""
    message = await update.callback_query.message.reply_text("🔄 جاري تحديث دورات Real Discount...")

    try:
        # جلب دورات Real Discount
        real_discount_courses = await fetch_real_discount_courses()

        # عرض نتائج التحديث
        await message.edit_text(
            f"✅ تم تحديث دورات Real Discount بنجاح!\n\n"
            f"🔥 دورات Real Discount المدفوعة: {len(real_discount_courses)}\n"
            f"🕰️ وقت التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Real Discount: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث دورات Real Discount")

async def update_all_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث جميع الدورات (من جميع المواقع)"""
    message = await update.callback_query.message.reply_text("🔄 جاري تحديث جميع الدورات...")

    try:
        # جلب الدورات العربية من Comidoc
        ar_courses = await fetch_all_courses('ar')

        # جلب دورات ديسكوديمي
        discudemy_courses = await fetch_discudemy_courses()

        # جلب دورات Real Discount
        real_discount_courses = await fetch_real_discount_courses()

        # عرض نتائج التحديث
        await message.edit_text(
            f"✅ تم تحديث جميع الدورات بنجاح!\n\n"
            f"🇸🇦 دورات Comidoc العربية: {len(ar_courses)}\n"
            f"💰 دورات Discudemy المدفوعة: {len(discudemy_courses)}\n"
            f"🔥 دورات Real Discount المدفوعة: {len(real_discount_courses)}\n\n"
            f"📊 إجمالي الدورات: {len(ar_courses) + len(discudemy_courses) + len(real_discount_courses)}\n"
            f"🕰️ وقت التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    except Exception as e:
        logger.error(f"خطأ في تحديث جميع الدورات: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث جميع الدورات")

async def show_admin_panel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض لوحة المشرف"""
    # إنشاء أزرار متجاورة للوحة المشرف
    row1 = [
        InlineKeyboardButton("📊 إحصائيات", callback_data='admin_stats'),
        InlineKeyboardButton("📢 نشر في القناة", callback_data='publish_channel')
    ]

    row2 = [
        InlineKeyboardButton("🧹 تنظيف الكوبونات", callback_data='clean_expired'),
        InlineKeyboardButton("🔄 تحديث الدورات", callback_data='update_courses')
    ]

    row3 = [
        InlineKeyboardButton("🔍 التحقق من الكوبونات", callback_data='verify_coupons'),
        InlineKeyboardButton("🗑️ تنظيف الذاكرة", callback_data='clear_cache')
    ]

    row4 = [
        InlineKeyboardButton("👥 إدارة أعضاء VIP", callback_data='manage_vip'),
        InlineKeyboardButton("📤 إرسال رسالة للقناة", callback_data='send_message_channel')
    ]

    row5 = [
        InlineKeyboardButton("➕ إضافة دورة جديدة", callback_data='add_new_course')
    ]

    keyboard = [row1, row2, row3, row4, row5]

    await update.callback_query.message.reply_text(
        "⚙️ لوحة المشرف\nاختر إحدى العمليات:",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def publish_to_channel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """نشر الدورات في القناة"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري نشر الدورات في القناة...")

    try:
        # قراءة الدورات من ملف comidoc
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                comidoc_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            comidoc_courses = []

        # قراءة الدورات من ملف discudemy
        try:
            with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
                discudemy_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            discudemy_courses = []

        # قراءة الدورات من ملف real.discount
        try:
            with open(Config.REAL_DISCOUNT_COURSES_FILE, 'r', encoding='utf-8') as f:
                real_discount_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            real_discount_courses = []

        # دمج جميع الدورات
        all_courses = comidoc_courses + discudemy_courses + real_discount_courses
        logger.info(f"تم تحميل {len(comidoc_courses)} دورة من comidoc و {len(discudemy_courses)} دورة من discudemy و {len(real_discount_courses)} دورة من real.discount")

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # الحصول على روابط الدورات المرسلة سابقًا
        sent_links = [course.get('link') for course in sent_courses]

        # تصفية الدورات التي لم يتم إرسالها بعد ولديها كوبونات متبقية
        new_courses = [
            course for course in all_courses
            if course.get('link') not in sent_links
            and course.get('coupons_left', 0) > 0
        ]

        if not new_courses:
            await message.edit_text("❌ لا توجد دورات جديدة للنشر")
            return

        # نشر الدورات الجديدة (بحد أقصى 5 دورات)
        courses_to_publish = new_courses[:5]
        published_count = 0

        for course in courses_to_publish:
            try:
                # تحضير نص الرسالة بالتنسيق الاحترافي
                language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

                # إضافة إيموجي عشوائي للعنوان
                title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
                random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
                random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

                # تحديد سعر الدورة الأصلي من البيانات المستخرجة
                original_price = course.get('original_price', "49.99$")

                # تحديد تاريخ النشر (التاريخ الحالي)
                current_date = datetime.now().strftime("%Y-%m-%d")

                message_text = (
                    f"{random_emoji1} <b>كورس جديد متاح مجاناً!</b> {random_emoji2}\n\n"
                    f"📝 <b>{course['title']}</b>\n\n"
                    f"💰 <b>السعر الأصلي:</b> {original_price}\n"
                    f"🎁 <b>السعر الآن:</b> مجاني (خصم 100%)\n"
                    f"🔑 <b>كوبون:</b> مفعل تلقائي\n"
                    f"🏢 <b>المنصة:</b> Udemy\n"
                    f"📅 <b>تاريخ النشر:</b> {current_date}\n"
                    f"👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', '100')}\n"
                    f"🌐 <b>اللغة:</b> {language_flag}\n\n"
                    f"⚡ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡"
                )

                # إنشاء أزرار
                keyboard = [
                    [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course['link'])],
                    [InlineKeyboardButton("📢 قناة الدورات", url='https://t.me/dqw45dd')]
                ]

                if CHANNEL_USERNAME:
                    keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

                reply_markup = InlineKeyboardMarkup(keyboard)

                # استخدام معرف القناة الصحيح
                channel_id = -1002613463650  # معرف القناة الصحيح

                # إرسال الرسالة إلى القناة مع الصورة إذا كانت متوفرة
                if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                    # إرسال الرسالة مع الصورة
                    await context.bot.send_photo(
                        chat_id=channel_id,
                        photo=course['thumbnail'],
                        caption=message_text,
                        reply_markup=reply_markup,
                        parse_mode='HTML'  # استخدام HTML للتنسيق الغامق
                    )
                else:
                    # إرسال الرسالة بدون صورة
                    await context.bot.send_message(
                        chat_id=channel_id,
                        text=message_text,
                        reply_markup=reply_markup,
                        disable_web_page_preview=True,
                        parse_mode='HTML'  # استخدام HTML للتنسيق الغامق
                    )

                # إضافة الدورة إلى قائمة الدورات المرسلة
                course['sent_date'] = datetime.now(timezone.utc).isoformat()
                sent_courses.append(course)
                published_count += 1

                # انتظار قليلاً لتجنب تجاوز حدود التليجرام
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في نشر الدورة {course.get('title')}: {e}")

        # حفظ الدورات المرسلة
        with open(Config.SENT_COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(sent_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(f"✅ تم نشر {published_count} دورة في القناة بنجاح")

    except Exception as e:
        logger.error(f"خطأ في نشر الدورات: {e}")
        await message.edit_text("❌ حدث خطأ في نشر الدورات")

async def show_stats(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض إحصائيات الدورات"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري جمع الإحصائيات...")

    try:
        # قراءة الدورات من الملف
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                all_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            all_courses = []

        # قراءة دورات ديسكوديمي
        try:
            with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
                discudemy_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            discudemy_courses = []

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # حساب الإحصائيات
        total_comidoc_courses = len(all_courses)
        ar_courses = len([c for c in all_courses if c.get('language') == 'ar'])

        total_discudemy_courses = len(discudemy_courses)
        active_discudemy_courses = len([c for c in discudemy_courses if c.get('coupons_left', 0) > 0])

        active_comidoc_courses = len([c for c in all_courses if c.get('coupons_left', 0) > 0])
        sent_count = len(sent_courses)

        total_all_courses = total_comidoc_courses + total_discudemy_courses
        total_active_courses = active_comidoc_courses + active_discudemy_courses

        # تحضير نص الإحصائيات
        stats_text = (
            "📊 إحصائيات الدورات\n\n"
            f"إجمالي الدورات: {total_all_courses}\n"
            f"🇸🇦 دورات عربية (comidoc): {ar_courses}\n"
            f"💰 دورات ديسكوديمي المدفوعة: {total_discudemy_courses}\n"
            f"✅ دورات نشطة: {total_active_courses}\n"
            f"📢 دورات تم نشرها: {sent_count}\n\n"
            f"تفاصيل الدورات النشطة:\n"
            f"- دورات comidoc: {active_comidoc_courses}\n"
            f"- دورات ديسكوديمي: {active_discudemy_courses}\n"
        )

        await message.edit_text(stats_text)

    except Exception as e:
        logger.error(f"خطأ في عرض الإحصائيات: {e}")
        await message.edit_text("❌ حدث خطأ في جمع الإحصائيات")

async def show_vip_management(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض واجهة إدارة أعضاء VIP بتنسيق جدول احترافي"""
    if not await admin_only(update, context):
        return

    # قراءة قائمة أعضاء VIP
    vip_users = vip_manager.get_all_vip_users()

    # إنشاء رسالة تعرض قائمة الأعضاء بتنسيق جدول
    message_text = "� <b>لوحة إدارة أعضاء VIP</b>\n\n"

    if vip_users:
        # إنشاء رأس الجدول
        message_text += "<pre>┌─────────────────────────────────────────────────────┐\n"
        message_text += "│ المعرف       │ الاسم           │ الحالة  │ تاريخ الانتهاء   │\n"
        message_text += "├─────────────────────────────────────────────────────┤\n"

        # إضافة بيانات الأعضاء
        for user_id, user_info in vip_users.items():
            name = user_info.get("name", "غير معروف")
            # اقتصار الاسم على 15 حرفًا كحد أقصى
            if len(name) > 15:
                name = name[:12] + "..."

            expiry_date = user_info.get("expires_at", "غير معروف")

            # حساب الأيام المتبقية والحالة
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                if days_left > 0:
                    status = "✅ نشط"
                    days_text = f"({days_left} يوم)"
                else:
                    status = "❌ منتهي"
                    days_text = "(منتهي)"
            except:
                status = "❓"
                days_text = "(غير معروف)"

            # تنسيق المعرف ليكون بطول ثابت
            user_id_str = str(user_id)
            if len(user_id_str) > 10:
                user_id_str = user_id_str[:7] + "..."

            # إضافة الصف إلى الجدول
            message_text += f"│ {user_id_str:<10} │ {name:<15} │ {status:<6} │ {expiry_date} {days_text:<8} │\n"

        # إغلاق الجدول
        message_text += "└─────────────────────────────────────────────────────┘</pre>\n\n"

        # إضافة إحصائيات
        active_users = sum(1 for _, info in vip_users.items() if
                          datetime.strptime(info.get("expires_at", "2000-01-01"), "%Y-%m-%d").date() > datetime.now().date())

        message_text += f"<b>📊 الإحصائيات:</b>\n"
        message_text += f"• إجمالي الأعضاء: {len(vip_users)}\n"
        message_text += f"• الأعضاء النشطين: {active_users}\n"
        message_text += f"• الاشتراكات المنتهية: {len(vip_users) - active_users}\n\n"
    else:
        message_text += "<i>لا يوجد أعضاء VIP حالياً.</i>\n\n"

    # إضافة أزرار للإدارة
    message_text += "<b>🛠️ إدارة الأعضاء:</b>\n"
    message_text += "• <b>إضافة عضو جديد:</b>\n"
    message_text += "<code>/add_vip معرف_المستخدم عدد_الأيام الاسم</code>\n"
    message_text += "مثال: <code>/add_vip 123456789 30 محمد أحمد</code>\n\n"
    message_text += "• <b>حذف عضو:</b>\n"
    message_text += "<code>/remove_vip معرف_المستخدم</code>\n"
    message_text += "مثال: <code>/remove_vip 123456789</code>\n\n"
    message_text += "• <b>تمديد اشتراك:</b>\n"
    message_text += "<code>/extend_vip معرف_المستخدم عدد_الأيام</code>\n"
    message_text += "مثال: <code>/extend_vip 123456789 30</code>"

    # إنشاء أزرار للعودة إلى لوحة المشرف
    keyboard = [
        [InlineKeyboardButton("🔄 تحديث القائمة", callback_data='manage_vip')],
        [InlineKeyboardButton("🔙 العودة للوحة المشرف", callback_data='admin_panel')]
    ]

    await update.callback_query.message.reply_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_send_message_form(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض نموذج إرسال رسالة إلى القناة"""
    if not await admin_only(update, context):
        return

    message_text = """
📤 <b>إرسال رسالة إلى القناة</b>

لإرسال رسالة إلى القناة، استخدم الأمر التالي:
<code>/send_to_channel الرسالة التي تريد إرسالها</code>

<b>ملاحظات:</b>
• يمكنك استخدام تنسيق HTML في الرسالة
• يمكنك إضافة روابط وإيموجي
• الرسالة ستظهر كما هي في القناة
    """

    # إنشاء أزرار للعودة إلى لوحة المشرف
    keyboard = [[InlineKeyboardButton("🔙 العودة للوحة المشرف", callback_data='admin_panel')]]

    await update.callback_query.message.reply_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_add_course_form(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض نموذج إضافة دورة جديدة"""
    if not await admin_only(update, context):
        return

    message_text = """
➕ <b>إضافة دورة جديدة</b>

لإضافة دورة جديدة، استخدم الأمر التالي:
<code>/add_course رابط_الكوبون رابط_الصورة السعر_الأصلي</code>

<b>مثال:</b>
<code>/add_course https://www.udemy.com/course/example/?couponCode=FREE2024 https://img-c.udemycdn.com/course/750x422/123456_abcd.jpg 49.99</code>

<b>ملاحظات:</b>
• يجب أن يحتوي رابط الكوبون على couponCode
• رابط الصورة يجب أن يكون صالحاً ومباشراً
• السعر الأصلي بالدولار (رقم فقط)
• سيتم استخراج عنوان الدورة تلقائياً من رابط Udemy
    """

    # إنشاء أزرار للعودة إلى لوحة المشرف
    keyboard = [[InlineKeyboardButton("🔙 العودة للوحة المشرف", callback_data='admin_panel')]]

    await update.callback_query.message.reply_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def clean_expired_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تنظيف الكوبونات المنتهية"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري تنظيف الكوبونات المنتهية...")

    try:
        # قراءة الدورات من الملف
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                all_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            all_courses = []

        # عدد الدورات قبل التنظيف
        before_count = len(all_courses)

        # تصفية الدورات التي لديها كوبونات متبقية فقط
        active_courses = [course for course in all_courses if course.get('coupons_left', 0) > 0]

        # عدد الدورات بعد التنظيف
        after_count = len(active_courses)
        removed_count = before_count - after_count

        # حفظ الدورات النشطة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(active_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(
            f"✅ تم تنظيف الكوبونات المنتهية بنجاح\n"
            f"تم إزالة {removed_count} دورة منتهية\n"
            f"الدورات النشطة المتبقية: {after_count}"
        )

    except Exception as e:
        logger.error(f"خطأ في تنظيف الكوبونات المنتهية: {e}")
        await message.edit_text("❌ حدث خطأ في تنظيف الكوبونات المنتهية")

def run_async_task(coroutine):
    """تشغيل مهمة غير متزامنة في حلقة أحداث جديدة"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coroutine)
    finally:
        loop.close()

def update_ar_courses_job():
    """وظيفة تحديث الدورات العربية للمجدول"""
    run_async_task(fetch_all_courses('ar'))

def verify_coupons_job():
    """وظيفة التحقق من صلاحية الكوبونات المخزنة"""
    logger.info("بدء التحقق من صلاحية الكوبونات المخزنة...")
    try:
        # قراءة الدورات المخزنة
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        if not courses:
            logger.info("لا توجد دورات مخزنة للتحقق منها")
            return

        # عدد الدورات قبل التحقق
        before_count = len(courses)
        logger.info(f"التحقق من {before_count} دورة مخزنة")

        # قائمة الدورات المحدثة
        updated_courses = []

        # التحقق من كل دورة
        for course in courses:
            try:
                # التحقق من رابط الدورة
                udemy_link = course.get('link')
                if not udemy_link:
                    continue

                # التحقق من حالة الكوبون
                coupon_status = check_course_coupon(course.get('comidoc_link', ''))

                # إذا كان الكوبون لا يزال صالحاً
                if coupon_status == "paid_with_coupon":
                    # التحقق من عدد الكوبونات المتبقية والسعر الأصلي
                    _, coupons_left, _, original_price = extract_udemy_link_and_coupons_left(course.get('comidoc_link', ''))

                    # تحديث عدد الكوبونات المتبقية والسعر الأصلي
                    course['coupons_left'] = coupons_left
                    course['original_price'] = original_price
                    course['last_verified'] = datetime.now(timezone.utc).isoformat()

                    # إضافة الدورة إلى القائمة المحدثة إذا كان لا يزال هناك كوبونات متبقية
                    if coupons_left > 0:
                        updated_courses.append(course)
                        logger.debug(f"الدورة {course['title']} لا تزال صالحة مع {coupons_left} كوبون متبقي")
                    else:
                        logger.info(f"تم استنفاد كوبونات الدورة: {course['title']}")
                else:
                    logger.info(f"انتهت صلاحية كوبون الدورة: {course['title']}")

            except Exception as e:
                logger.error(f"خطأ في التحقق من الدورة {course.get('title', '')}: {e}")
                # في حالة الخطأ، نحتفظ بالدورة كما هي
                updated_courses.append(course)

        # حفظ الدورات المحدثة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(updated_courses, f, ensure_ascii=False, indent=2)

        # عدد الدورات بعد التحقق
        after_count = len(updated_courses)
        removed_count = before_count - after_count

        logger.info(f"اكتمل التحقق من الكوبونات. تمت إزالة {removed_count} دورة منتهية. تبقى {after_count} دورة صالحة.")

    except Exception as e:
        logger.error(f"خطأ في وظيفة التحقق من الكوبونات: {e}")

async def fetch_discudemy_courses():
    """جلب الدورات من موقع discudemy.com"""
    try:
        logger.info("بدء جلب الدورات من موقع discudemy.com...")

        # إنشاء كائن DiscudemyScraper
        scraper = DiscudemyScraper(
            base_url=Config.DISCUDEMY_URL,
            max_pages=Config.DISCUDEMY_MAX_PAGES,
            timeout=Config.REQUEST_TIMEOUT,
            concurrent_requests=Config.CONCURRENT_REQUESTS
        )

        # جلب الدورات
        courses = await scraper.fetch_all_courses()

        # حفظ الدورات في الملف
        if courses:
            with open(Config.DISCUDEMY_COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            logger.info(f"تم حفظ {len(courses)} دورة من موقع discudemy.com")
        else:
            logger.warning("لم يتم العثور على دورات في موقع discudemy.com")

        return courses

    except Exception as e:
        logger.error(f"خطأ في جلب دورات discudemy.com: {e}")
        return []

async def get_discudemy_courses():
    """جلب الدورات من ملف discudemy المحفوظ"""
    try:
        # قراءة الدورات من الملف المحفوظ
        with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        # إضافة الحقول المفقودة للدورات من Discudemy
        processed_courses = []
        for course in courses:
            # إضافة الحقول المفقودة
            if 'coupons_left' not in course:
                course['coupons_left'] = 100  # قيمة افتراضية للكوبونات المتبقية
            if 'language' not in course:
                course['language'] = 'ar'  # افتراض أن الدورات عربية
            if 'status' not in course:
                course['status'] = 'paid_with_coupon'  # حالة الكوبون

            # التحقق من أن الدورة لديها رابط صالح
            if course.get('link') and 'couponCode=' in course.get('link', ''):
                processed_courses.append(course)

        logger.info(f"تم العثور على {len(processed_courses)} دورة من موقع discudemy.com")
        return processed_courses

    except FileNotFoundError:
        logger.warning(f"ملف دورات discudemy غير موجود. جاري جلب الدورات...")
        # إذا لم يكن الملف موجودًا، قم بجلب الدورات
        return await fetch_discudemy_courses()

    except Exception as e:
        logger.error(f"خطأ في جلب دورات discudemy: {e}")
        return []

async def fetch_real_discount_courses():
    """جلب الدورات من موقع real.discount"""
    try:
        logger.info("بدء جلب الدورات من موقع real.discount...")

        # إنشاء كائن RealDiscountScraper
        scraper = RealDiscountScraper()

        # جلب الدورات
        courses = scraper.run()

        # حفظ الدورات في الملف
        if courses:
            with open(Config.REAL_DISCOUNT_COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            logger.info(f"تم حفظ {len(courses)} دورة من موقع real.discount")
        else:
            logger.warning("لم يتم العثور على دورات في موقع real.discount")

        return courses

    except Exception as e:
        logger.error(f"خطأ في جلب دورات real.discount: {e}")
        return []

async def get_real_discount_courses():
    """جلب الدورات من ملف real.discount المحفوظ"""
    try:
        # قراءة الدورات من الملف المحفوظ
        with open(Config.REAL_DISCOUNT_COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        # إضافة الحقول المفقودة للدورات من Real Discount
        processed_courses = []
        for course in courses:
            # إضافة الحقول المفقودة
            if 'coupons_left' not in course:
                course['coupons_left'] = 100  # قيمة افتراضية للكوبونات المتبقية
            if 'language' not in course:
                course['language'] = 'ar'  # افتراض أن الدورات عربية
            if 'status' not in course:
                course['status'] = 'paid_with_coupon'  # حالة الكوبون

            # التحقق من أن الدورة لديها رابط صالح
            if course.get('link'):
                processed_courses.append(course)

        logger.info(f"تم العثور على {len(processed_courses)} دورة من موقع real.discount")
        return processed_courses

    except FileNotFoundError:
        logger.warning(f"ملف دورات real.discount غير موجود. جاري جلب الدورات...")
        # إذا لم يكن الملف موجودًا، قم بجلب الدورات
        return await fetch_real_discount_courses()

    except Exception as e:
        logger.error(f"خطأ في جلب دورات real.discount: {e}")
        return []

def update_discudemy_courses_job():
    """وظيفة تحديث دورات discudemy للمجدول"""
    run_async_task(fetch_discudemy_courses())

def schedule_updates():
    """جدولة تحديث الدورات والتحقق من الكوبونات"""
    # جدولة تحديث الدورات
    scheduler.add_job(
        func=update_ar_courses_job,
        trigger='interval',
        minutes=15,
        id='update_ar_courses',
        replace_existing=True
    )

    # جدولة تحديث دورات discudemy
    scheduler.add_job(
        func=update_discudemy_courses_job,
        trigger='interval',
        minutes=30,
        id='update_discudemy_courses',
        replace_existing=True
    )

    # جدولة التحقق من صلاحية الكوبونات كل ساعتين
    scheduler.add_job(
        func=verify_coupons_job,
        trigger='interval',
        hours=2,
        id='verify_coupons',
        replace_existing=True
    )

    scheduler.start()

async def get_courses_by_language(language='ar'):
    """جلب الدورات حسب اللغة المحددة من الملف المحفوظ"""
    try:
        # قراءة الدورات من الملف المحفوظ
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            all_courses = json.load(f)

        # تصفية الدورات حسب اللغة
        filtered_courses = [
            course for course in all_courses
            if course.get('language') == language and course.get('coupons_left', 0) > 0
        ]

        logger.info(f"تم العثور على {len(filtered_courses)} دورة بلغة {language}")
        return filtered_courses

    except FileNotFoundError:
        logger.warning(f"ملف الدورات غير موجود. جاري جلب الدورات...")
        # إذا لم يكن الملف موجودًا، قم بجلب الدورات
        return await fetch_all_courses(language)

    except Exception as e:
        logger.error(f"خطأ في جلب الدورات: {e}")
        return []

# تعديل الأوامر للتعامل مع الدورات حسب اللغة
async def arabic_courses_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر لعرض الدورات العربية"""
    courses = await get_courses_by_language('ar')
    if courses:
        # عكس ترتيب الدورات من الأسفل إلى الأعلى
        courses = list(reversed(courses))
        await update.message.reply_text(f"تم العثور على {len(courses)} دورة عربية. سيتم عرض الدورات بترتيب عكسي (من الأسفل إلى الأعلى):")
        for course in courses:
            await send_course_message(update, course)
    else:
        await update.message.reply_text("❌ لم يتم العثور على دورات عربية متاحة")

# تم إزالة وظيفة الدورات الإنجليزية

async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة الأخطاء"""
    logger.error(f"Exception while handling an update: {context.error}")
    try:
        # إضافة معلومات عن التحديث الذي تسبب في الخطأ
        error_message = f"⚠️ حدث خطأ في البوت:\n{context.error}"

        if update:
            user_info = f"المستخدم: {update.effective_user.id if update.effective_user else 'غير معروف'}"
            chat_info = f"المحادثة: {update.effective_chat.id if update.effective_chat else 'غير معروفة'}"
            error_message += f"\n{user_info}\n{chat_info}"

        # إرسال رسالة للمشرفين
        for admin_id in ADMIN_IDS:
            await context.bot.send_message(
                chat_id=admin_id,
                text=error_message
            )
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار الخطأ للمشرفين: {e}")

async def handle_vip_subscribe(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة طلب الاشتراك في خدمة VIP"""
    query = update.callback_query
    user_id = update.effective_user.id

    # التحقق مما إذا كان المستخدم VIP بالفعل
    if vip_manager.is_vip(user_id):
        await query.message.reply_text(
            "✅ أنت بالفعل مشترك في خدمة VIP!\n"
            "يمكنك الاستمتاع بالروابط المباشرة بدون اختصار.\n"
            "استخدم الأمر /vip_info لعرض تفاصيل اشتراكك."
        )
        return

    # قائمة من العناوين المتنوعة
    vip_titles = [
        "💎 <b>اشترك في خدمة VIP الآن!</b> 💎",
        "✨ <b>ارتقِ بتجربتك مع عضوية VIP!</b> ✨",
        "🌟 <b>احصل على تجربة تعليمية مميزة مع VIP!</b> 🌟",
        "🚀 <b>انطلق نحو التعلم بدون حدود مع VIP!</b> 🚀",
        "👑 <b>كن ملك التعلم مع عضوية VIP!</b> 👑",
        "🔥 <b>عروض VIP الحصرية بانتظارك!</b> 🔥"
    ]

    # قائمة من المميزات المتنوعة
    vip_features = [
        "✅ روابط مباشرة للدورات بدون اختصار",
        "✅ تجاوز صفحات الإعلانات المزعجة",
        "✅ دعم فني مميز وأولوية في الرد",
        "✅ وصول فوري للكورسات الجديدة",
        "✅ تنبيهات حصرية بأفضل الكورسات",
        "✅ تجربة استخدام سلسة وسريعة",
        "✅ توفير الوقت والجهد في البحث عن الكورسات"
    ]

    # قائمة من الجمل التحفيزية
    vip_motivations = [
        "🔹 استثمر في نفسك بأقل من 4 سنتات يومياً!",
        "🔹 وفر وقتك وجهدك مقابل دولار واحد شهرياً!",
        "🔹 لا تضيع وقتك في صفحات الإعلانات المزعجة!",
        "🔹 انضم لنخبة المتعلمين المميزين!",
        "🔹 احصل على أفضل تجربة تعليمية بسعر زهيد!",
        "🔹 العرض محدود! سارع بالاشتراك الآن!"
    ]

    # اختيار عنوان عشوائي
    title = random.choice(vip_titles)

    # اختيار 4-5 مميزات عشوائية
    selected_features = random.sample(vip_features, random.randint(4, 5))

    # اختيار 2-3 جمل تحفيزية
    selected_motivations = random.sample(vip_motivations, random.randint(2, 3))

    # إنشاء رسالة الاشتراك بتنسيق جذاب
    message = (
        f"{title}\n\n"
        "<b>🌟 المميزات الحصرية:</b>\n"
        f"{chr(10).join(selected_features)}\n\n"
        "<b>� لماذا تشترك الآن؟</b>\n"
        f"{chr(10).join(selected_motivations)}\n\n"
        "🔶 <b>سعر الاشتراك:</b> 1$ شهرياً فقط!\n\n"
        "📲 للاشتراك، تواصل مع المسؤول:\n"
        "👤 @CiH99x"
    )

    # إنشاء أزرار
    keyboard = [
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url="https://t.me/CiH99x")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def handle_vip_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة طلب عرض معلومات VIP من خلال الأزرار"""
    query = update.callback_query
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # التحقق مما إذا كان المستخدم VIP
    if vip_manager.is_vip(user_id):
        # الحصول على معلومات VIP
        vip_info = vip_manager.get_vip_info(user_id)

        if vip_info:
            joined_date = vip_info.get("joined_at", "غير محدد")
            expiry_date = vip_info.get("expires_at", "غير محدد")
            days_total = vip_info.get("days_total", 0)

            # حساب الأيام المتبقية
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
            except:
                days_left_text = "غير محدد"

            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                f"📅 تاريخ الاشتراك: {joined_date}\n"
                f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                f"⌛ المدة المتبقية: {days_left_text}\n"
                f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
        else:
            # في حالة كان المستخدم مشرفًا (VIP تلقائي)
            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
    else:
        # قائمة من العناوين المتنوعة للمستخدمين غير VIP
        non_vip_titles = [
            "💎 <b>اكتشف مزايا عضوية VIP!</b> 💎",
            "✨ <b>ارتقِ بتجربتك التعليمية مع VIP!</b> ✨",
            "🚀 <b>حان وقت الانضمام لعائلة VIP!</b> 🚀",
            "🌟 <b>تجربة تعليمية استثنائية تنتظرك!</b> 🌟",
            "👑 <b>كن من نخبة المتعلمين مع VIP!</b> 👑"
        ]

        # قائمة من المميزات المتنوعة
        non_vip_features = [
            "✓ روابط مباشرة للدورات بدون اختصار",
            "✓ تجاوز صفحات الإعلانات المزعجة",
            "✓ دعم فني مميز وأولوية في الرد",
            "✓ وصول فوري للكورسات الجديدة",
            "✓ تنبيهات حصرية بأفضل الكورسات",
            "✓ تجربة استخدام سلسة وسريعة",
            "✓ توفير الوقت والجهد في البحث عن الكورسات"
        ]

        # قائمة من الجمل التحفيزية
        non_vip_motivations = [
            "🔸 استثمر في نفسك بأقل من 4 سنتات يومياً!",
            "🔸 وفر وقتك وجهدك مقابل دولار واحد شهرياً!",
            "🔸 لا تضيع وقتك في صفحات الإعلانات المزعجة!",
            "🔸 انضم لنخبة المتعلمين المميزين!",
            "🔸 احصل على أفضل تجربة تعليمية بسعر زهيد!",
            "🔸 العرض محدود! سارع بالاشتراك الآن!"
        ]

        # اختيار عنوان عشوائي
        title = random.choice(non_vip_titles)

        # اختيار 3-4 مميزات عشوائية
        selected_features = random.sample(non_vip_features, random.randint(3, 4))

        # اختيار 2 جمل تحفيزية
        selected_motivations = random.sample(non_vip_motivations, 2)

        # إنشاء رسالة بتنسيق جذاب
        message = (
            f"{title}\n\n"
            "❗ <b>أنت غير مشترك في خدمة VIP</b> ❗\n\n"
            "<b>🌟 ما الذي ستحصل عليه مع VIP؟</b>\n"
            f"{chr(10).join(selected_features)}\n\n"
            "<b>� لماذا تشترك الآن؟</b>\n"
            f"{chr(10).join(selected_motivations)}\n\n"
            "🔶 <b>سعر الاشتراك:</b> 1$ شهرياً فقط!\n\n"
            "📲 للاشتراك، تواصل مع المسؤول:\n"
            "👤 @CiH99x"
        )

    # إنشاء أزرار
    keyboard = [
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url="https://t.me/CiH99x")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def vip_info_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض معلومات VIP للمستخدم"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # التحقق مما إذا كان المستخدم VIP
    if vip_manager.is_vip(user_id):
        # الحصول على معلومات VIP
        vip_info = vip_manager.get_vip_info(user_id)

        if vip_info:
            joined_date = vip_info.get("joined_at", "غير محدد")
            expiry_date = vip_info.get("expires_at", "غير محدد")
            days_total = vip_info.get("days_total", 0)

            # حساب الأيام المتبقية
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
            except:
                days_left_text = "غير محدد"

            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                f"📅 تاريخ الاشتراك: {joined_date}\n"
                f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                f"⌛ المدة المتبقية: {days_left_text}\n"
                f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
        else:
            # في حالة كان المستخدم مشرفًا (VIP تلقائي)
            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
    else:
        # رسالة للمستخدمين غير VIP
        message = (
            "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
            "❌ أنت غير مشترك في خدمة VIP.\n\n"
            "مميزات الاشتراك في خدمة VIP:\n"
            "• الحصول على روابط مباشرة للدورات بدون اختصار\n"
            "• تجاوز صفحات الإعلانات\n"
            "• دعم فني مميز\n\n"
            "للاشتراك في الخدمة، يرجى التواصل مع المسؤول:\n"
            "👤 @CiH99x\n\n"
            "سعر الاشتراك: 1$ شهرياً"
        )

    # إنشاء أزرار
    keyboard = [
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url="https://t.me/CiH99x")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def add_vip_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إضافة مستخدم VIP (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    # التحقق من وجود المعلمات المطلوبة
    if not context.args or len(context.args) < 2:
        await update.message.reply_text(

            "/add_vip 123456789 30"
        )
        return

    try:
        # استخراج المعلمات
        user_id = int(context.args[0])
        days = int(context.args[1])
        name = " ".join(context.args[2:]) if len(context.args) > 2 else f"VIP User {user_id}"

        # إضافة المستخدم
        if vip_manager.add_vip(user_id, name, days):
            expiry_date = (datetime.now() + timedelta(days=days)).strftime("%Y-%m-%d")
            await update.message.reply_text(
                f"✅ تم إضافة عضو VIP جديد بنجاح!\n\n"
                f"👤 الاسم: {name}\n"
                f"🆔 المعرف: {user_id}\n"
                f"⏳ المدة: {days} يوم\n"
                f"📅 تاريخ الانتهاء: {expiry_date}"
            )
        else:
            await update.message.reply_text("❌ حدث خطأ أثناء إضافة العضو")

    except ValueError:
        await update.message.reply_text("❌ تأكد من صحة المعرف وعدد الأيام")
    except Exception as e:
        await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

async def send_to_channel_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر إرسال الدورات إلى القناة (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    message = await update.message.reply_text("جاري إرسال الدورات إلى القناة...")

    try:
        # قراءة الدورات من الملف
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            all_courses = json.load(f)

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # الحصول على روابط الدورات المرسلة سابقًا
        sent_links = [course.get('link') for course in sent_courses]

        # تصفية الدورات التي لم يتم إرسالها بعد ولديها كوبونات متبقية
        new_courses = [
            course for course in all_courses
            if course.get('link') not in sent_links
            and course.get('coupons_left', 0) > 0
            and course.get('language') == 'ar'  # فقط الدورات العربية
        ]

        if not new_courses:
            await message.edit_text("❌ لا توجد دورات جديدة للنشر")
            return

        # نشر الدورات الجديدة (بحد أقصى 5 دورات)
        courses_to_publish = new_courses[:5]
        published_count = 0

        for course in courses_to_publish:
            try:
                # تحضير نص الرسالة بالتنسيق الاحترافي
                language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

                # إضافة إيموجي عشوائي للعنوان
                title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
                random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
                random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

                # تحديد سعر الدورة الأصلي من البيانات المستخرجة
                original_price = course.get('original_price', "49.99$")

                # تحديد تاريخ النشر (التاريخ الحالي)
                current_date = datetime.now().strftime("%Y-%m-%d")

                # اختصار الرابط للقناة (دائمًا نستخدم الرابط المختصر للقناة)
                course_link = shorten_url(course['link'])

                message_text = (
                    f"{random_emoji1} <b>كورس جديد متاح مجاناً!</b> {random_emoji2}\n\n"
                    f"📝 <b>{course['title']}</b>\n\n"
                    f"💰 <b>السعر الأصلي:</b> {original_price}\n"
                    f"🎁 <b>السعر الآن:</b> مجاني (خصم 100%)\n"
                    f"🔑 <b>كوبون:</b> مفعل تلقائي\n"
                    f"🏢 <b>المنصة:</b> Udemy\n"
                    f"📅 <b>تاريخ النشر:</b> {current_date}\n"
                    f"👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', '100')}\n"
                    f"🌐 <b>اللغة:</b> {language_flag}\n\n"
                    f"💎 <b>للحصول على روابط مباشرة بدون اختصار، اشترك في خدمة VIP!</b>\n\n"
                    f"⚡ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡"
                )

                # إنشاء أزرار
                keyboard = [
                    [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)]
                ]

                if CHANNEL_USERNAME:
                    keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

                # إضافة زر للاشتراك في خدمة VIP
                keyboard.append([InlineKeyboardButton("💎 اشترك في خدمة VIP", url="https://t.me/CiH99x")])

                reply_markup = InlineKeyboardMarkup(keyboard)

                # استخدام معرف القناة الصحيح
                channel_id = -1002613463650  # معرف القناة الصحيح

                # إرسال الرسالة إلى القناة مع الصورة إذا كانت متوفرة
                if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                    # إرسال الرسالة مع الصورة
                    await context.bot.send_photo(
                        chat_id=channel_id,
                        photo=course['thumbnail'],
                        caption=message_text,
                        reply_markup=reply_markup,
                        parse_mode='HTML'  # استخدام HTML للتنسيق الغامق
                    )
                else:
                    # إرسال الرسالة بدون صورة
                    await context.bot.send_message(
                        chat_id=channel_id,
                        text=message_text,
                        reply_markup=reply_markup,
                        disable_web_page_preview=True,
                        parse_mode='HTML'  # استخدام HTML للتنسيق الغامق
                    )

                # إضافة الدورة إلى قائمة الدورات المرسلة
                course['sent_date'] = datetime.now(timezone.utc).isoformat()
                sent_courses.append(course)
                published_count += 1

                # انتظار قليلاً لتجنب تجاوز حدود التليجرام
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في نشر الدورة {course.get('title')}: {e}")

        # حفظ الدورات المرسلة
        with open(Config.SENT_COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(sent_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(f"✅ تم نشر {published_count} دورة في القناة بنجاح")

    except Exception as e:
        logger.error(f"خطأ في نشر الدورات: {e}")
        await message.edit_text("❌ حدث خطأ في نشر الدورات")

async def clean_courses_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر تنظيف الكوبونات المنتهية (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    message = await update.message.reply_text("جاري تنظيف الكوبونات المنتهية...")

    try:
        # قراءة الدورات من الملف
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                all_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            all_courses = []

        # عدد الدورات قبل التنظيف
        before_count = len(all_courses)

        # تصفية الدورات التي لديها كوبونات متبقية فقط
        active_courses = [course for course in all_courses if course.get('coupons_left', 0) > 0]

        # عدد الدورات بعد التنظيف
        after_count = len(active_courses)
        removed_count = before_count - after_count

        # حفظ الدورات النشطة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(active_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(
            f"✅ تم تنظيف الكوبونات المنتهية بنجاح\n"
            f"تم إزالة {removed_count} دورة منتهية\n"
            f"الدورات النشطة المتبقية: {after_count}"
        )

    except Exception as e:
        logger.error(f"خطأ في تنظيف الكوبونات المنتهية: {e}")
        await message.edit_text("❌ حدث خطأ في تنظيف الكوبونات المنتهية")

async def courses_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر عرض الدورات المجانية"""
    await update.message.reply_text("جاري تحميل الدورات المجانية...")

    # استخدام نفس وظيفة عرض الدورات العربية
    courses = await get_courses_by_language('ar')
    if courses:
        # عكس ترتيب الدورات من الأسفل إلى الأعلى
        courses = list(reversed(courses))
        await update.message.reply_text(f"تم العثور على {len(courses)} دورة مجانية. سيتم عرض الدورات بترتيب عكسي (من الأسفل إلى الأعلى):")
        for course in courses:
            await send_course_message(update, course)
    else:
        await update.message.reply_text("❌ لم يتم العثور على دورات مجانية متاحة")

async def check_new_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر التحقق من وجود دورات جديدة"""
    message = await update.message.reply_text("جاري التحقق من وجود دورات جديدة...")

    try:
        # قراءة الدورات الحالية
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                old_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            old_courses = []

        old_links = [course.get('link') for course in old_courses]
        old_count = len(old_courses)

        # جلب الدورات الجديدة
        new_courses = await fetch_all_courses('ar')
        new_count = len(new_courses)

        # حساب عدد الدورات الجديدة
        new_links = [course.get('link') for course in new_courses]
        actually_new_courses = [course for course in new_courses if course.get('link') not in old_links]
        new_added_count = len(actually_new_courses)

        # عرض النتائج
        if new_added_count > 0:
            await message.edit_text(
                f"✅ تم العثور على {new_added_count} دورة جديدة!\n"
                f"إجمالي الدورات الآن: {new_count}\n"
                f"سيتم عرض أحدث الدورات الجديدة:"
            )

            # عرض الدورات الجديدة (بحد أقصى 5)
            for course in actually_new_courses[:5]:
                await send_course_message(update, course)
        else:
            await message.edit_text(
                f"ℹ️ لم يتم العثور على دورات جديدة.\n"
                f"إجمالي الدورات الحالية: {new_count}"
            )

    except Exception as e:
        logger.error(f"خطأ في التحقق من الدورات الجديدة: {e}")
        await message.edit_text("❌ حدث خطأ أثناء التحقق من الدورات الجديدة")

async def add_course_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر إضافة دورة جديدة (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    # التحقق من وجود المعلمات المطلوبة
    if not context.args or len(context.args) < 3:
        await update.message.reply_text(
            "❌ استخدام خاطئ للأمر!\n\n"
            "الاستخدام الصحيح:\n"
            "<code>/add_course رابط_الكوبون رابط_الصورة السعر_الأصلي</code>\n\n"
            "مثال:\n"
            "<code>/add_course https://www.udemy.com/course/example/?couponCode=FREE2024 https://img-c.udemycdn.com/course/750x422/123456_abcd.jpg 49.99</code>",
            parse_mode='HTML'
        )
        return

    try:
        # استخراج المعلمات
        course_link = context.args[0]
        thumbnail = context.args[1]
        original_price = context.args[2]

        # التحقق من صحة رابط الكوبون
        if not course_link.startswith('https://www.udemy.com/course/') or 'couponCode=' not in course_link:
            await update.message.reply_text("❌ رابط الكوبون غير صالح! يجب أن يكون رابط Udemy ويحتوي على couponCode")
            return

        # التحقق من صحة رابط الصورة
        if not thumbnail.startswith(('http://', 'https://')):
            await update.message.reply_text("❌ رابط الصورة غير صالح!")
            return

        # التحقق من صحة السعر
        try:
            price_value = float(original_price)
            if price_value <= 0:
                raise ValueError("السعر يجب أن يكون أكبر من صفر")
        except ValueError:
            await update.message.reply_text("❌ السعر غير صالح! يجب أن يكون رقماً موجباً")
            return

        message = await update.message.reply_text("⏳ جاري إضافة الدورة الجديدة...")

        # استخراج عنوان الدورة من رابط Udemy
        try:
            response = requests.get(course_link, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن عنوان الدورة
            title_element = soup.find('h1', {'data-purpose': 'lead-title'}) or soup.find('title')
            if title_element:
                title = title_element.get_text().strip()
                # تنظيف العنوان من النصوص الإضافية
                title = title.replace(' | Udemy', '').strip()
            else:
                # استخراج العنوان من الرابط كبديل
                course_slug = course_link.split('/course/')[1].split('/')[0]
                title = course_slug.replace('-', ' ').title()
        except Exception as e:
            logger.error(f"خطأ في استخراج عنوان الدورة: {e}")
            # استخراج العنوان من الرابط كبديل
            course_slug = course_link.split('/course/')[1].split('/')[0]
            title = course_slug.replace('-', ' ').title()

        # إنشاء كائن الدورة الجديدة
        new_course = {
            'title': title,
            'link': course_link,
            'thumbnail': thumbnail,
            'coupons_left': 100,  # قيمة افتراضية
            'original_price': f"${original_price}",
            'language': 'ar',  # افتراض أن الدورة عربية
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'paid_with_coupon',
            'source': 'manual_add'  # مصدر يدوي
        }

        # قراءة الدورات الحالية
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            courses = []

        # التحقق من عدم وجود الدورة مسبقاً
        if any(c.get('link') == course_link for c in courses):
            await message.edit_text("⚠️ هذه الدورة موجودة بالفعل في قاعدة البيانات!")
            return

        # إضافة الدورة الجديدة
        courses.append(new_course)

        # حفظ الدورات المحدثة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(
            f"✅ تم إضافة الدورة الجديدة بنجاح!\n\n"
            f"📝 العنوان: {title}\n"
            f"💰 السعر الأصلي: ${original_price}\n"
            f"🔗 الرابط: {course_link[:50]}...\n"
            f"📅 تاريخ الإضافة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

        logger.info(f"تم إضافة دورة جديدة يدوياً: {title}")

    except Exception as e:
        logger.error(f"خطأ في إضافة الدورة الجديدة: {e}")
        await update.message.reply_text(f"❌ حدث خطأ أثناء إضافة الدورة: {str(e)}")

def main():
    """تشغيل البوت"""
    # إنشاء التطبيق
    application = Application.builder().token(TOKEN).build()

    try:
        # التحقق من المتغيرات البيئية
        check_environment_variables()

        # تهيئة الملفات
        initialize_files()

        # إضافة معالجات الأوامر
        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("ar", arabic_courses_command))
        application.add_handler(CommandHandler("check_new", check_new_command))
        application.add_handler(CommandHandler("vip_info", vip_info_command))
        application.add_handler(CommandHandler("add_vip", add_vip_command))
        application.add_handler(CommandHandler("add_course", add_course_command))
        application.add_handler(CommandHandler("send_to_channel", send_to_channel_command))
        application.add_handler(CommandHandler("clean_courses", clean_courses_command))
        application.add_handler(CallbackQueryHandler(button))

        # إضافة معالج الأخطاء
        application.add_error_handler(error_handler)

        # بدء البوت
        logger.info("Starting bot...")

        # تشغيل البوت في وضع الاستطلاع
        application.run_polling(allowed_updates=Update.ALL_TYPES)

        return application

    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        raise

if __name__ == '__main__':
    try:
        # تشغيل البوت بشكل مباشر
        main()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")