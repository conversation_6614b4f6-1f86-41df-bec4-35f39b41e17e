# دليل إضافة الدورات الجديدة

## نظرة عامة
تم إضافة ميزة جديدة لإضافة الدورات يدوياً من خلال لوحة المشرف في البوت. هذه الميزة تسمح للمشرفين بإضافة دورات جديدة بسرعة وسهولة.

## كيفية الوصول للميزة

### الطريقة الأولى: من خلال لوحة المشرف
1. ابدأ محادثة مع البوت بالأمر `/start`
2. اضغط على زر "⚙️ لوحة المشرف" (متاح للمشرفين فقط)
3. اضغط على زر "➕ إضافة دورة جديدة"
4. ستظهر لك التعليمات والأمر المطلوب

### الطريقة الثانية: الأمر المباشر
استخدم الأمر التالي مباشرة:
```
/add_course رابط_الكوبون رابط_الصورة السعر_الأصلي
```

## مثال عملي
```
/add_course https://www.udemy.com/course/python-programming/?couponCode=FREE2024 https://img-c.udemycdn.com/course/750x422/123456_abcd.jpg 49.99
```

## متطلبات البيانات

### 1. رابط الكوبون
- يجب أن يكون رابط Udemy صالح
- يجب أن يحتوي على `couponCode=` في الرابط
- مثال: `https://www.udemy.com/course/example/?couponCode=FREE2024`

### 2. رابط الصورة
- يجب أن يكون رابط مباشر للصورة
- يفضل استخدام صور من Udemy نفسه
- مثال: `https://img-c.udemycdn.com/course/750x422/123456_abcd.jpg`

### 3. السعر الأصلي
- رقم فقط بدون رمز الدولار
- يجب أن يكون أكبر من صفر
- مثال: `49.99`

## ما يحدث عند إضافة الدورة

1. **استخراج العنوان**: يتم استخراج عنوان الدورة تلقائياً من صفحة Udemy
2. **التحقق من التكرار**: يتم التحقق من عدم وجود الدورة مسبقاً
3. **إضافة البيانات**: يتم إضافة الدورة مع البيانات التالية:
   - العنوان (مستخرج تلقائياً)
   - الرابط
   - الصورة
   - السعر الأصلي
   - عدد الكوبونات المتبقية (100 افتراضياً)
   - اللغة (عربية افتراضياً)
   - تاريخ الإضافة
   - حالة الكوبون (مدفوع مع كوبون)
   - المصدر (إضافة يدوية)

## رسائل النجاح والخطأ

### رسالة النجاح
```
✅ تم إضافة الدورة الجديدة بنجاح!

📝 العنوان: اسم الدورة
💰 السعر الأصلي: $49.99
🔗 الرابط: https://www.udemy.com/course/example...
📅 تاريخ الإضافة: 2024-07-24 20:25:30
```

### رسائل الخطأ المحتملة
- ❌ استخدام خاطئ للأمر! (عند عدم إدخال المعلمات المطلوبة)
- ❌ رابط الكوبون غير صالح! (عند إدخال رابط غير صحيح)
- ❌ رابط الصورة غير صالح! (عند إدخال رابط صورة غير صحيح)
- ❌ السعر غير صالح! (عند إدخال سعر غير صحيح)
- ⚠️ هذه الدورة موجودة بالفعل في قاعدة البيانات! (عند التكرار)

## ملاحظات مهمة

1. **الصلاحيات**: هذه الميزة متاحة للمشرفين فقط
2. **التحقق التلقائي**: يتم التحقق من صحة جميع البيانات المدخلة
3. **الحفظ التلقائي**: يتم حفظ الدورة في ملف `courses.json` تلقائياً
4. **العرض الفوري**: الدورة ستظهر فوراً في قائمة الدورات العربية
5. **إمكانية الإرسال**: يمكن إرسال الدورة للقناة مثل باقي الدورات

## استكشاف الأخطاء

### إذا لم يتم استخراج العنوان بشكل صحيح
- سيتم استخدام اسم الدورة من الرابط كبديل
- يمكن تعديل العنوان لاحقاً من ملف `courses.json`

### إذا كان رابط الصورة لا يعمل
- تأكد من أن الرابط مباشر للصورة
- جرب نسخ رابط الصورة من صفحة الدورة على Udemy

### إذا ظهرت رسالة خطأ غير متوقعة
- تحقق من سجلات البوت للحصول على تفاصيل أكثر
- تأكد من أن جميع المعلمات صحيحة

## أمثلة إضافية

### دورة برمجة Python
```
/add_course https://www.udemy.com/course/complete-python-bootcamp/?couponCode=PYTHON2024 https://img-c.udemycdn.com/course/750x422/567890_abcd.jpg 89.99
```

### دورة تصميم الجرافيك
```
/add_course https://www.udemy.com/course/graphic-design-masterclass/?couponCode=DESIGN2024 https://img-c.udemycdn.com/course/750x422/111222_efgh.jpg 79.99
```

### دورة التسويق الرقمي
```
/add_course https://www.udemy.com/course/digital-marketing-course/?couponCode=MARKETING2024 https://img-c.udemycdn.com/course/750x422/333444_ijkl.jpg 99.99
```

## 🗑️ حذف الدورات

### نظرة عامة
تم إضافة ميزة حذف الدورات لحل مشكلة الدورات المنتهية الصلاحية. الدورات المضافة يدوياً لا يتم التحقق من كوبوناتها تلقائياً، لذلك يجب حذفها يدوياً عند انتهاء صلاحيتها.

### كيفية الوصول لميزة الحذف

#### الطريقة الأولى: من خلال لوحة المشرف
1. ابدأ محادثة مع البوت بالأمر `/start`
2. اضغط على زر "⚙️ لوحة المشرف"
3. اضغط على زر "🗑️ حذف دورة"
4. ستظهر لك التعليمات والأمر المطلوب

#### الطريقة الثانية: الأمر المباشر
```
/delete_course رابط_الدورة
```

### مثال عملي للحذف
```
/delete_course https://www.udemy.com/course/enterprise-storage-concepts-and-practice-dell-unity-truenas/?couponCode=FREE5DAYS
```

### متطلبات الحذف
- **رابط الدورة الكامل**: يجب إدخال رابط الدورة كاملاً مع الكوبون
- **صلاحيات المشرف**: الميزة متاحة للمشرفين فقط
- **التأكد من الرابط**: العملية نهائية ولا يمكن التراجع عنها

### ما يحدث عند حذف الدورة

1. **البحث عن الدورة**: يتم البحث عن الدورة في قاعدة البيانات
2. **الحذف من الملفات**: يتم حذف الدورة من:
   - ملف `courses.json` (الدورات الرئيسية)
   - ملف `sent_courses.json` (الدورات المرسلة للقناة)
3. **تأكيد الحذف**: يتم عرض رسالة تأكيد مع تفاصيل الدورة المحذوفة

### رسائل النجاح والخطأ

#### رسالة النجاح
```
✅ تم حذف الدورة بنجاح!

📝 العنوان: Enterprise Storage Concepts...
🔗 الرابط: https://www.udemy.com/course/enterprise...
📅 تاريخ الحذف: 2024-07-24 20:45:30
📊 عدد الدورات المتبقية: 15
```

#### رسائل الخطأ المحتملة
- ❌ استخدام خاطئ للأمر! (عند عدم إدخال الرابط)
- ❌ رابط الدورة غير صالح! (عند إدخال رابط غير صحيح)
- ❌ لم يتم العثور على الدورة في قاعدة البيانات! (عند عدم وجود الدورة)
- ⚠️ هذه الميزة متاحة للمشرفين فقط (عند عدم وجود صلاحيات)

### متى يجب حذف الدورة؟

1. **انتهاء صلاحية الكوبون**: عندما يصبح الكوبون غير فعال
2. **إزالة الدورة من Udemy**: عندما تُحذف الدورة من المنصة
3. **خطأ في الإضافة**: عند إضافة دورة خاطئة بالخطأ
4. **تنظيف قاعدة البيانات**: للحفاظ على نظافة قائمة الدورات

### نصائح مهمة

1. **تحقق من الرابط**: تأكد من الرابط قبل الحذف
2. **اختبر الكوبون**: جرب الكوبون قبل الحذف للتأكد من انتهاء صلاحيته
3. **احتفظ بنسخة احتياطية**: يُنصح بعمل نسخة احتياطية من ملفات الدورات
4. **العملية نهائية**: لا يمكن استرداد الدورة بعد الحذف

### الفرق بين الإضافة والحذف

| العملية | الإضافة | الحذف |
|---------|---------|-------|
| **المعلمات المطلوبة** | رابط + صورة + سعر | رابط فقط |
| **التحقق التلقائي** | لا يوجد | لا يوجد |
| **إمكانية التراجع** | يمكن الحذف لاحقاً | لا يمكن التراجع |
| **التأثير على الملفات** | إضافة للملفات | حذف من الملفات |
